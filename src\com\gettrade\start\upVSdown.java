package com.gettrade.start;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.Socket;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import com.binance.api.client.api.sync.BinanceApiFuturesRestClient;
import com.binance.api.client.api.sync.BinanceApiSpotRestClient;
import com.binance.api.client.domain.market.Candlestick;
import com.binance.api.client.domain.market.CandlestickInterval;
import com.binance.api.client.factory.BinanceAbstractFactory;
import com.binance.api.client.factory.BinanceFuturesApiClientFactory;
import com.binance.api.client.factory.BinanceSpotApiClientFactory;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.stream.JsonReader;
import com.sun.jna.Library;
import com.sun.jna.Native;

public class upVSdown {

    public interface Trade extends Library {
        Trade INSTANCE = (Trade) Native.loadLibrary("MTrade", Trade.class);

        public void TradeDlg(String psRunName, String psTitleName, String psTime);

        public void TradeStart(int nflag);

        public void GetUpDownSec();

        public void GetUpDownSelMoney(int value);

        public int GetUpDownSecRcv();

        public void SetUpDownSecRcv();
    }

    // ADX 클래스 - 평균 방향성 지수 계산
    static class ADX {
        private double currentADX = Double.NaN;
        private double plusDI = Double.NaN;
        private double minusDI = Double.NaN;
        private final int period;

        public ADX(int period) {
            this.period = period;
        }

        public void calculate(List<Candlestick> candlesticks) {
            if (candlesticks == null || candlesticks.size() < period + 1) {
                currentADX = Double.NaN;
                plusDI = Double.NaN;
                minusDI = Double.NaN;
                return;
            }

            int size = candlesticks.size();
            double[] trueRanges = new double[size];
            double[] plusDMs = new double[size];
            double[] minusDMs = new double[size];

            // 첫 번째 값은 계산할 수 없으므로 건너뜀
            for (int i = 1; i < size; i++) {
                Candlestick current = candlesticks.get(i);
                Candlestick previous = candlesticks.get(i - 1);

                // True Range 계산
                trueRanges[i] = getTrueRange(current, previous);

                // Directional Movement 계산
                double[] dms = getDirectionalMovement(current, previous);
                plusDMs[i] = dms[0];
                minusDMs[i] = dms[1];
            }

            // Wilder's Smoothing 적용
            double smoothedTR = 0;
            double smoothedPlusDM = 0;
            double smoothedMinusDM = 0;

            // 초기 값 계산 (첫 번째 기간의 합)
            for (int i = 1; i <= period; i++) {
                smoothedTR += trueRanges[i];
                smoothedPlusDM += plusDMs[i];
                smoothedMinusDM += minusDMs[i];
            }

            // 첫 번째 평균 값
            double[] diValues = new double[size];
            double[] dxValues = new double[size];

            // 첫 번째 +DI 및 -DI 계산
            if (smoothedTR > 0) {
                diValues[period] = 100 * (smoothedPlusDM / smoothedTR);
                double minusDI_temp = 100 * (smoothedMinusDM / smoothedTR);
                // DX 계산
                double dx = 100 * (Math.abs(diValues[period] - minusDI_temp) / (diValues[period] + minusDI_temp));
                dxValues[period] = dx;
            } else {
                diValues[period] = 0;
                dxValues[period] = 0;
            }

            // 나머지 기간에 대한 계산
            for (int i = period + 1; i < size; i++) {
                // Wilder's Smoothing 공식 적용
                smoothedTR = smoothedTR - (smoothedTR / period) + trueRanges[i];
                smoothedPlusDM = smoothedPlusDM - (smoothedPlusDM / period) + plusDMs[i];
                smoothedMinusDM = smoothedMinusDM - (smoothedMinusDM / period) + minusDMs[i];

                // +DI 및 -DI 계산
                double plusDI_temp = 0;
                double minusDI_temp = 0;
                if (smoothedTR > 0) {
                    plusDI_temp = 100 * (smoothedPlusDM / smoothedTR);
                    minusDI_temp = 100 * (smoothedMinusDM / smoothedTR);
                }

                // DX 계산
                double dx = 0;
                if ((plusDI_temp + minusDI_temp) > 0) {
                    dx = 100 * (Math.abs(plusDI_temp - minusDI_temp) / (plusDI_temp + minusDI_temp));
                }
                dxValues[i] = dx;

                diValues[i] = plusDI_temp;
            }

            // ADX 계산 (DX의 평균)
            double adxSum = 0;
            for (int i = period; i < 2 * period; i++) {
                if (i < size) {
                    adxSum += dxValues[i];
                }
            }
            double initialADX = adxSum / period;

            // 나머지 ADX 값 계산
            double adx = initialADX;
            for (int i = 2 * period; i < size; i++) {
                adx = ((period - 1) * adx + dxValues[i]) / period;
            }

            // 최종 값 설정
            this.currentADX = adx;
            this.plusDI = diValues[size - 1];
            this.minusDI = smoothedTR > 0 ? 100 * (smoothedMinusDM / smoothedTR) : 0;
        }

        private double getTrueRange(Candlestick current, Candlestick previous) {
            double high = Double.parseDouble(current.getHigh());
            double low = Double.parseDouble(current.getLow());
            double prevClose = Double.parseDouble(previous.getClose());

            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);

            return Math.max(Math.max(tr1, tr2), tr3);
        }

        private double[] getDirectionalMovement(Candlestick current, Candlestick previous) {
            double currentHigh = Double.parseDouble(current.getHigh());
            double currentLow = Double.parseDouble(current.getLow());
            double previousHigh = Double.parseDouble(previous.getHigh());
            double previousLow = Double.parseDouble(previous.getLow());

            double upMove = currentHigh - previousHigh;
            double downMove = previousLow - currentLow;

            double plusDM = 0;
            double minusDM = 0;

            if (upMove > downMove && upMove > 0) {
                plusDM = upMove;
            }

            if (downMove > upMove && downMove > 0) {
                minusDM = downMove;
            }

            return new double[] { plusDM, minusDM };
        }

        public double getCurrentADX() {
            return currentADX;
        }

        public double getPlusDI() {
            return plusDI;
        }

        public double getMinusDI() {
            return minusDI;
        }

        public String getTrendDirection() {
            if (Double.isNaN(plusDI) || Double.isNaN(minusDI)) {
                return "Unknown";
            }
            if (plusDI > minusDI) {
                return "Uptrend";
            } else if (minusDI > plusDI) {
                return "Downtrend";
            } else {
                return "No Trend";
            }
        }
    }

    public static int BUY_UP = 300005;
    public static int SELL_DOWN = 300006;
    public static int CONFORM = 300007;
    public static int WINDOWS_TOP = 300008;

    public static int TIME_RESET = 4;
    public static int REFRESH = 304;
    public static int TRADE_STOP = 103;
    public static int REFRESH_CNT = 0;
    public static volatile double volume = 0;
    public static volatile double volumeBTC = 0;
    public static double volumeBTC_Check = 15;

    public static double trade_volume_max = 15;
    public static int trade_set_money = 0;
    public static double BollUp = 0.0;
    public static double BollDown = 0.0;

    // ADX 및 ATR 관련 변수 추가
    public static volatile double currentADX = Double.NaN;
    public static volatile double atrValue = Double.NaN;
    public static volatile double currentSpotPrice = Double.NaN;
    public static volatile double currentFuturesPrice = Double.NaN;
    public static ADX adx14Calculator = new ADX(14);

    public static int BollCheck = 0;
    public static int checkboll = 0;
    public static int TradeCntValue = 3;
    public static int TradeCntSleep = 1000;
    public static int TradeFlg = 0;
    public static int NextSleep = 10000;
    public static int CheckSleep = 10000;
    public static int GAME_TIME = 5;
    public static int INIT_WAIT = 0;
    public static int TRACE = 0;

    // ATR 계산 메소드 추가
    private static double calculateATR(List<Candlestick> candlesticks, int period) {
        if (candlesticks == null || candlesticks.size() < period + 1) {
            return Double.NaN;
        }

        int size = candlesticks.size();
        double[] trueRanges = new double[size];

        // 첫 번째 값은 계산할 수 없으므로 건너뜀
        for (int i = 1; i < size; i++) {
            Candlestick current = candlesticks.get(i);
            Candlestick previous = candlesticks.get(i - 1);

            double high = Double.parseDouble(current.getHigh());
            double low = Double.parseDouble(current.getLow());
            double prevClose = Double.parseDouble(previous.getClose());

            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);

            trueRanges[i] = Math.max(Math.max(tr1, tr2), tr3);
        }

        // Wilder's Smoothing 적용
        double smoothedTR = 0;

        // 초기 값 계산 (첫 번째 기간의 합)
        for (int i = 1; i <= period; i++) {
            smoothedTR += trueRanges[i];
        }

        // 첫 번째 ATR 값
        double atr = smoothedTR / period;

        // 나머지 기간에 대한 계산
        for (int i = period + 1; i < size; i++) {
            // Wilder's Smoothing 공식 적용
            atr = ((period - 1) * atr + trueRanges[i]) / period;
        }

        return atr;
    }

    public static BinanceFuturesApiClientFactory BinancefactoryF = null;
    public static BinanceApiFuturesRestClient clientF = null;

    public static BinanceSpotApiClientFactory factory = null;
    public static BinanceApiSpotRestClient client = null;
    private static AtomicBoolean initTrade = new AtomicBoolean(false);
    public static String symbol = "BTCUSDT";

    private static final ExecutorService PrintService = Executors.newSingleThreadExecutor();

    public static void asyncPrint(String message) {
        PrintService.submit(() -> System.out.println(message));
    }

    static class MessageHandler implements Runnable {
        private String[] orderbook20MSg = new String[6]; // 더 많은 데이터를 저장하기 위해 크기 증가
        private boolean bfirstBTC20 = false;
        private boolean bfirstFBTC20 = false;

        private double trade_orderbook_down_F20 = Double.NaN;
        private double trade_orderbook_up_F20 = Double.NaN;
        private double trade_orderbook_down20 = Double.NaN;
        private double trade_orderbook_up20 = Double.NaN;

        @Override
        public void run() {
            while (true) {
                try (Socket socket = new Socket("127.0.0.1", 22222)) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                    //out = new BufferedWriter(new OutputStreamWriter(socket.getOutputStream()));
                    while (true) {
                        String message = in.readLine();
                        if (message != null && TradeFlg > 0) {
                            //messageProcessingService.submit(() -> {
                            processMessage(message);
                            //});
                        } else {
                            resetTradeOrderbook();
                            resetFlags();
                        }
                    }
                } catch (IOException e) {
                    asyncPrint("Socket connection error: " + e.getMessage());
                    if (TRACE > 1)
                        e.printStackTrace();
                    resetTradeOrderbook();
                    resetFlags();

                    try {
                        Thread.sleep(5000); // Wait 5 seconds before attempting to reconnect
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        if (TRACE > 1)
                            ie.printStackTrace();
                    }
                }
            }
        }

        private void processMessage(String message) {
            if (TradeFlg == 0) {
                resetTradeOrderbook();
                resetFlags();
                return;
            }

            if (message != null) {
                try {
                    // JsonReader를 사용하여 JSON 파싱 개선
                    JsonReader jsonReader = new JsonReader(new StringReader(message));
                    jsonReader.setLenient(true);
                    JsonObject jsonObject = JsonParser.parseReader(jsonReader).getAsJsonObject();

                    if (jsonObject.has("Mtype") && jsonObject.has("message")) {
                        String mtype = jsonObject.get("Mtype").getAsString();
                        if ("BTC20".equals(mtype)) {
                            bfirstBTC20 = true;
                            String msg = jsonObject.get("message").getAsString();
                            orderbook20MSg = msg.split("-"); // 더 많은 데이터 추출
                            trade_orderbook_down20 = Double.parseDouble(orderbook20MSg[0]);
                            trade_orderbook_up20 = Double.parseDouble(orderbook20MSg[1]);
                            currentSpotPrice = Double.parseDouble(orderbook20MSg[2]);
                        } else if ("FBTC20".equals(mtype)) {
                            bfirstFBTC20 = true;
                            String msg = jsonObject.get("message").getAsString();
                            orderbook20MSg = msg.split("-"); // 더 많은 데이터 추출
                            trade_orderbook_down_F20 = Double.parseDouble(orderbook20MSg[0]);
                            trade_orderbook_up_F20 = Double.parseDouble(orderbook20MSg[1]);
                            currentFuturesPrice = Double.parseDouble(orderbook20MSg[2]);
                        }

                        // 데이터 유효성 검증
                        if (!validateOrderbookData()) {
                            if (TRACE > 1)
                                asyncPrint("Invalid orderbook data detected. Skipping trade check.");
                            return;
                        }

                        if (TradeFlg > 0) {
                            if (BollCheck == 1 && checkboll == 1) {
                                initTrade.set(true);
                                TradeFlg = 0;
                                return;
                            }

                            // 거래 조건 체크 및 실행
                            executeTrade();
                        }

                        if (TRACE == 1) {
                            asyncPrint("TRACE volume:" + volume + " volumeBTC:" + volumeBTC + " up20:"
                                    + trade_orderbook_up20 + " dn20:" + trade_orderbook_down20 + " up_F20:"
                                    + trade_orderbook_up_F20 + " dn_F20:" + trade_orderbook_down_F20
                                    + " ADX:" + currentADX + " ATR:" + atrValue);
                        }
                    }
                } catch (Exception e) {
                    asyncPrint("Error processing message: " + e.getMessage());
                    if (TRACE > 1)
                        e.printStackTrace();
                }
            }
        }

        // 데이터 유효성 검증 메소드 추가
        private boolean validateOrderbookData() {
            return !Double.isNaN(trade_orderbook_up_F20) && !Double.isNaN(trade_orderbook_down_F20)
                    && !Double.isNaN(trade_orderbook_up20) && !Double.isNaN(trade_orderbook_down20)
                    && bfirstFBTC20 && bfirstBTC20;
        }

        // 거래 실행 로직을 별도 메소드로 분리
        private void executeTrade() {
            // Aldar.java의 거래 조건 적용
            if (TradeFlg > 0 && INIT_WAIT > 0) {
                // ADX, ATR, 거래량 조건 체크
                if (currentADX < 20 && atrValue < 0.0005 && volume < 100 && volumeBTC < 10) {
                    // 매수 조건: 오더북 업 < 1
                    if (trade_orderbook_up20 < 1 && trade_orderbook_up_F20 < 1) {
                        if (initTrade.compareAndSet(false, true)) {
                            Trade.INSTANCE.TradeStart(BUY_UP);
                            TradeFlg = 0;
                            asyncPrint("BUY_UP signal sent. ADX:" + currentADX + " ATR:" + atrValue
                                    + " volume:" + volume + " volumeBTC:" + volumeBTC);
                        }
                    }
                    // 매도 조건: 오더북 다운 < 1
                    else if (trade_orderbook_down20 < 1 && trade_orderbook_down_F20 < 1) {
                        if (initTrade.compareAndSet(false, true)) {
                            Trade.INSTANCE.TradeStart(SELL_DOWN);
                            TradeFlg = 0;
                            asyncPrint("SELL_DOWN signal sent. ADX:" + currentADX + " ATR:" + atrValue
                                    + " volume:" + volume + " volumeBTC:" + volumeBTC);
                        }
                    }
                }
            }
        }

        private void resetTradeOrderbook() {
            trade_orderbook_down_F20 = Double.NaN;
            trade_orderbook_up_F20 = Double.NaN;
            trade_orderbook_down20 = Double.NaN;
            trade_orderbook_up20 = Double.NaN;
        }

        private void resetFlags() {
            bfirstBTC20 = false;
            bfirstFBTC20 = false;
        }
    }

    // InitMain 메소드 추가 - ADX, ATR 계산 및 데이터 초기화
    private static void InitMain() {
        try {
            // 퓨처 캔들스틱 데이터 가져오기
            List<Candlestick> futuresCandlesticks = clientF.getCandlestickBars(symbol,
                    CandlestickInterval.ONE_MINUTE, 100, null, null);

            // 스팟 캔들스틱 데이터 가져오기
            List<Candlestick> spotCandlesticks = client.getCandlestickBars(symbol,
                    CandlestickInterval.ONE_MINUTE, 100, null, null);

            if (futuresCandlesticks == null || futuresCandlesticks.isEmpty() ||
                    spotCandlesticks == null || spotCandlesticks.isEmpty()) {
                asyncPrint("Failed to get candlestick data");
                return;
            }

            // 퓨처 거래량 계산
            volume = 0;
            Candlestick candlestick = null;
            List<Double> priceList = new ArrayList<>();
            for (int i = 0; i < futuresCandlesticks.size(); i++) {
                candlestick = futuresCandlesticks.get(i);
                if (i >= 9 && i < futuresCandlesticks.size() - 1)
                    volume += Double.valueOf(candlestick.getVolume()).doubleValue();

                priceList.add(Double.parseDouble(candlestick.getClose()));
            }
            volume = volume / 10;
            volume = roundToTwoDecimalPlaces(volume);

            // 스팟 거래량 계산
            volumeBTC = 0;
            for (int i = 0; i < spotCandlesticks.size(); i++) {
                candlestick = spotCandlesticks.get(i);
                if (i >= 9 && i < spotCandlesticks.size() - 1)
                    volumeBTC += Double.valueOf(candlestick.getVolume()).doubleValue();
            }

            volumeBTC = volumeBTC / 10;
            volumeBTC = roundToTwoDecimalPlaces(volumeBTC);

            // 볼린저 밴드 계산
            BollingerBands boll = new BollingerBands(priceList, 20, 2.0);
            BollDown = boll.getdownBollingerBands();
            BollUp = boll.getUpBollingerBands();

            // ADX 계산
            adx14Calculator.calculate(futuresCandlesticks);
            currentADX = adx14Calculator.getCurrentADX();

            // ATR 계산
            atrValue = calculateATR(futuresCandlesticks, 14);

            // 속성 파일 로드
            String filePath = "";
            if (GAME_TIME == 5)
                filePath = "c:/Trade/Tradevs.properties";
            else if (GAME_TIME == 15)
                filePath = "c:/Trade/Tradevs15.properties";
            else if (GAME_TIME == 30)
                filePath = "c:/Trade/Tradevs30.properties";

            InputStream input = new FileInputStream(filePath);
            Properties prop = new Properties();
            prop.load(input);

            volumeBTC_Check = Double.parseDouble(prop.getProperty("volumeBTC_Check", "15"));
            trade_volume_max = Integer.parseInt(prop.getProperty("trade_volume_max", "15"));
            TradeCntValue = Integer.parseInt(prop.getProperty("TradeCntValue", "2"));
            TradeCntSleep = Integer.parseInt(prop.getProperty("TradeCntSleep", "1000"));
            trade_set_money = Integer.parseInt(prop.getProperty("trade_set_money", "0"));
            checkboll = Integer.parseInt(prop.getProperty("checkboll", "0"));
            NextSleep = Integer.parseInt(prop.getProperty("NextSleep", "10000"));
            CheckSleep = Integer.parseInt(prop.getProperty("CheckSleep", "10000"));
            INIT_WAIT = Integer.parseInt(prop.getProperty("INIT_WAIT", "0"));
            TRACE = Integer.parseInt(prop.getProperty("TRACE", "0"));

            // 볼린저 밴드 체크
            if (Double.parseDouble(candlestick.getHigh()) > BollUp
                    || Double.parseDouble(candlestick.getLow()) < BollDown)
                BollCheck = 1;
            else
                BollCheck = 0;

            // 로그 출력
            asyncPrint("volumeBTC_Check:" + volumeBTC_Check);
            asyncPrint("trade_volume_max:" + trade_volume_max);
            asyncPrint("BollUp:" + BollUp);
            asyncPrint("BollDown:" + BollDown);
            asyncPrint("volume:" + volume);
            asyncPrint("volumeBTC:" + volumeBTC);
            asyncPrint("currentADX:" + currentADX);
            asyncPrint("atrValue:" + atrValue);
            asyncPrint("TradeCntValue:" + TradeCntValue);
            asyncPrint("TradeCntSleep:" + TradeCntSleep);
            asyncPrint("trade_set_money:" + trade_set_money);
            asyncPrint("checkboll:" + checkboll);
            asyncPrint("NextSleep:" + NextSleep);
            asyncPrint("CheckSleep:" + CheckSleep);
            asyncPrint("GAME_TIME:" + GAME_TIME);
            asyncPrint("INIT_WAIT:" + INIT_WAIT);
            asyncPrint("TRACE:" + TRACE);

        } catch (Exception e) {
            asyncPrint("Error in InitMain: " + e.getMessage());
            if (TRACE > 1)
                e.printStackTrace();
        }
    }

    public static void main(String[] args) throws InterruptedException, IOException {

        BinancefactoryF = BinanceAbstractFactory.createFuturesFactory("", "");
        clientF = BinancefactoryF.newRestClient();

        factory = BinanceAbstractFactory.createSpotFactory("", "");
        client = factory.newRestClient();

        String sRunName = args[0];
        String sTitle = args[1];
        String sTime = args[2];
        GAME_TIME = Integer.parseInt(sTime);

        Trade.INSTANCE.TradeDlg(sRunName, sTitle, sTime);

        Trade.INSTANCE.TradeStart(WINDOWS_TOP);
        Trade.INSTANCE.TradeStart(TIME_RESET);

        ScheduledExecutorService serviceGetData = Executors.newSingleThreadScheduledExecutor();
        AtomicBoolean isRunning = new AtomicBoolean(false);

        Runnable runnableGetData = new Runnable() {

            @Override
            public void run() {
                if (isRunning.compareAndSet(false, true)) {
                    try {
                        try {
                            Thread.sleep(5000);

                            if (REFRESH_CNT > 5 /*|| GAME_TIME > 5*/ ) {
                                //if(INIT_WAIT > 0)
                                //    Trade.INSTANCE.TradeStart(CONFORM);

                                Trade.INSTANCE.TradeStart(REFRESH);
                                REFRESH_CNT = 0;
                                Trade.INSTANCE.TradeStart(TIME_RESET);
                            }
                            REFRESH_CNT++;
                            Thread.sleep(NextSleep);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            if (TRACE > 1)
                                e.printStackTrace();
                        }

                        try {
                            // InitMain 메소드 호출로 대체
                            InitMain();
                        } catch (Exception e) {
                            asyncPrint("Failed to initialize data: " + e.getMessage());
                            if (TRACE > 1)
                                e.printStackTrace();
                            volume = 0;
                            return;
                        }

                        asyncPrint("== Wait Trade ==");
                        initTrade.set(false);
                        TradeFlg = 0;
                        Trade.INSTANCE.GetUpDownSelMoney(trade_set_money);
                        if (getCheckStart()) {
                            TradeFlg = 1;
                            asyncPrint("== START ==");
                            long startTime = System.nanoTime();
                            while (true) {

                                long endTime = System.nanoTime();
                                long elapsedTime = endTime - startTime;
                                double milliseconds = (double) elapsedTime / 1_000_000.0;
                                if (milliseconds >= CheckSleep) {
                                    break;
                                }
                                Thread.sleep(10);
                            }

                            TradeFlg = 0;
                        }
                    } catch (Exception e) {
                        asyncPrint("Error in data retrieval thread: " + e.getMessage());
                        if (TRACE > 1)
                            e.printStackTrace();
                        isRunning.set(false);
                    } finally {
                        isRunning.set(false);
                    }
                }
            }
        };

        serviceGetData.scheduleAtFixedRate(runnableGetData, 0, 200, TimeUnit.MILLISECONDS);

        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        //Trade.INSTANCE.TradeStart(1);

        Thread thread = new Thread(new MessageHandler());
        thread.start();

        while (true) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
    }

    private static double roundToTwoDecimalPlaces(double value) {
        DecimalFormat df = new DecimalFormat("#.####");
        return Double.parseDouble(df.format(value));
    }

    public static boolean getCheckStart() throws IOException {

        long startTime = System.nanoTime();

        while (true) {
            Trade.INSTANCE.GetUpDownSec();
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }

            int str = Trade.INSTANCE.GetUpDownSecRcv();
            if (TRACE == 1)
                asyncPrint("str: " + str);

            if (str <= TradeCntValue) {
                try {
                    Thread.sleep(TradeCntSleep);
                } catch (InterruptedException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
                //Trade.INSTANCE.SetUpDownSecRcv();
                return true;
            }

            long endTime = System.nanoTime();
            long elapsedTime = endTime - startTime;
            double seconds = (double) elapsedTime / 1_000_000_000.0;
            if (seconds * 1000 >= NextSleep)
                return false;
        }
    }
}