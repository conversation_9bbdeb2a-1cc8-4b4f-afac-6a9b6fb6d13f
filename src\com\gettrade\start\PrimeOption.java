package com.gettrade.start;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.Socket;
import java.text.DecimalFormat;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import com.binance.api.client.api.sync.BinanceApiFuturesRestClient;
import com.binance.api.client.api.sync.BinanceApiSpotRestClient;
import com.binance.api.client.domain.market.Candlestick;
import com.binance.api.client.domain.market.CandlestickInterval;
import com.binance.api.client.factory.BinanceAbstractFactory;
import com.binance.api.client.factory.BinanceFuturesApiClientFactory;
import com.binance.api.client.factory.BinanceSpotApiClientFactory;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.stream.JsonReader;
import com.sun.jna.Library;
import com.sun.jna.Native;

public class PrimeOption {

    public interface Trade extends Library {
        Trade INSTANCE = (Trade) Native.loadLibrary("MTrade", Trade.class);

        public void TradeBitqDlg_expert();
        public int GetUpDownSecRcv();
        public void TradeStart(int nflag, String value);
    }

    public static int BUY_UP = 500201;
    public static int SELL_DOWN = 500202;
    public static int CHECK = 500203;
    public static int SETMONEY = 500204;
    public static int WINDOWS_TOP = 300008;

    public static int TIME_RESET = 4;
    public static int REFRESH = 304;
    public static int TRADE_STOP = 103;
    public static int REFRESH_CNT = 0;
    public static double volume = 0; // 전체 퓨처 거래량 (과거 데이터 기반)
    public static double volumeBTC = 0; // 전체 스팟 거래량 (과거 데이터 기반)
    public static double volumeBTC_Check = 15; // 스팟 거래량 임계값 (설정값)

    public static double trade_volume_max = 15; // 퓨처 거래량 임계값 (설정값)
    public static double BollUp = 0.0;
    public static double BollDown = 0.0;
    public static int BollCheck = 0; // 볼린저 밴드 이탈 여부 (1: 이탈)
    public static int checkboll = 0; // 볼린저 밴드 체크 활성화 여부 (설정값)
    public static int TradeFlg = 0; // 거래 상태 플래그 (0: 대기, 1: 매수 시도, 2: 매도 시도, 3: 거래 중지)
    public static int INIT_WAIT = 0; // 초기 대기 시간 관련 플래그 (설정값)
    public static int TRACE = 0; // 디버깅 로그 레벨 (설정값)
    public static String MONEY = "2"; 

    public static BinanceFuturesApiClientFactory BinancefactoryF = null;
    public static BinanceApiFuturesRestClient clientF = null;

    public static BinanceSpotApiClientFactory factory = null;
    public static BinanceApiSpotRestClient client = null;
    private static AtomicBoolean initTrade = new AtomicBoolean(false); // 초기화 진행 중 플래그
    private static AtomicBoolean first = new AtomicBoolean(false); // 첫 메시지 수신 여부 플래그

    public static String symbol = "BTCUSDT";
    private static final ExecutorService messageProcessingService = Executors.newCachedThreadPool();
    private static final ExecutorService PrintService = Executors.newSingleThreadExecutor();

    public static void asyncPrint(String message) {
        PrintService.submit(() -> System.out.println(message));
    }

    // --- MessageHandler (Main Trading Logic) ---
    static class MessageHandler implements Runnable {
        // 오더북 및 가격 데이터
        private volatile double tradeOrderbookDownF20 = 99;
        private volatile double tradeOrderbookUpF20 = 99;
        private volatile double tradeOrderbookDown20 = 99;
        private volatile double tradeOrderbookUp20 = 99;
        private volatile double tradePriceF = -999999; // 선물 현재가
        private volatile double tradePrice = -999999; // 스팟 현재가
        private volatile double oldtradePriceF = -999999;
        private volatile double oldtradePrice = -999999;

        // 데이터 수신 플래그
        private volatile boolean bfirstBTC20 = false;
        private volatile boolean bfirstFBTC20 = false;
        
        // 거래량 이력 리스트를 가격 이력 리스트로 변경
        private final Deque<Double> spotPriceHistory = new ArrayDeque<>();
        private final Deque<Double> futuresPriceHistory = new ArrayDeque<>();
        private static final int MAX_PRICE_HISTORY = 5; // 최대 20개의 이력을 보관

        @Override
        public void run() {
            while (true) {
                try (Socket socket = new Socket("127.0.0.1", 22222)) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                    asyncPrint("Data receiving socket connection successful.");
                    resetDataFlags(); // 연결 시 플래그 초기화

                    while (true) {
                        String message = in.readLine();
                        if (message != null && !message.isEmpty()) {
                            long totalMilliseconds = System.currentTimeMillis();
                            long currentSecond = (totalMilliseconds / 1000) % 60;

                            // 매 분 59초 또는 첫 실행 시 초기화 작업 수행
                            if ((currentSecond == 59 || !first.get())) {
                                if (initTrade.compareAndSet(false, true)) {
                                    messageProcessingService.submit(() -> {
                                        try {
                                            Trade.INSTANCE.TradeStart(CHECK,""); // 상태 체크
                                            InitMain(); // 설정값 및 볼린저 밴드 등 업데이트
                                            resetTradeOrderbook(); // 오더북 데이터 초기화
                                            resetDataFlags(); // 데이터 수신 플래그 초기화 -> 연결 시 한 번만 수행하도록 변경
                                            if (TradeFlg == 3)
                                                TradeFlg = 0; // 거래 중지 상태 해제
                                            asyncPrint("Regular initialization completed (TradeFlg: "
                                                    + TradeFlg + ")");
                                            Thread.sleep(1000);
                                            if (Trade.INSTANCE.GetUpDownSecRcv() != 1) {
                                                INIT_WAIT = 0; // 설정 파일 반영
                                                asyncPrint("INIT_WAIT setting applied: " + INIT_WAIT);
                                            }
                                        } catch (Exception e) {
                                            asyncPrint("Error during initialization: " + e.getMessage());
                                            e.printStackTrace();
                                        } finally {
                                            first.set(true); // 첫 실행 완료 플래그 설정
                                            initTrade.set(false);
                                        }
                                    });
                                }
                            } else if (first.get()) { // 첫 실행 이후
                                if (initTrade.compareAndSet(false, true)) { // 다른 작업 중이 아닐 때만 메시지 처리
                                    try {
                                        processMessage(message); // 핵심 로직: 메시지 처리 및 거래 결정
                                    } catch (Exception e) {
                                        asyncPrint("Error while processing message: " + e.getMessage());
                                        e.printStackTrace();
                                    } finally {
                                        initTrade.set(false);
                                    }
                                }
                            }
                        } 
                    }
                } catch (IOException e) {
                    asyncPrint("Socket connection error or data receiving error: " + e.getMessage());
                    resetTradeOrderbook();
                    resetDataFlags();
                    spotPriceHistory.clear();
                    futuresPriceHistory.clear();
                    initTrade.set(false); // 작업 중 플래그 해제
                    first.set(false); // 첫 실행 플래그 리셋
                    try {
                        Thread.sleep(5000); // 5초 후 재연결 시도
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        asyncPrint("Interrupt occurred while waiting for reconnection.");
                    }
                } catch (Exception ex) {
                    asyncPrint("Unexpected error in MessageHandler loop: " + ex.getMessage());
                    ex.printStackTrace();
                    initTrade.set(false);
                    try {
                        Thread.sleep(1000); // 잠시 대기
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }

        /**
         * 수신된 메시지를 처리하고 거래 조건을 확인하여 거래를 실행합니다.
         */
        private void processMessage(String message) {

            if (message == null || message.isEmpty()) {
                asyncPrint("Empty message received, skipping processing.");
                return;
            }
            
            try (JsonReader reader = new JsonReader(new StringReader(message))) {
                reader.setLenient(true);

                // asyncPrint("메시지 수신: " + message); // 디버깅 시 활성화
                if (TradeFlg > 2) { // 거래 중지 상태이면 처리 안 함
                    return;
                }

                // 2. 기본 조건 확인 (isPriceStable 변수는 stabilityCheckExecutor가 업데이트)
                if (checkboll == 1 && BollCheck == 1) {
                    if (TRACE > 0)
                        asyncPrint("Bollinger Bands deviation, trading not allowed.");
                    TradeFlg = 3; // 거래 중지
                    spotPriceHistory.clear();
                    futuresPriceHistory.clear();
                    return;
                }

                // 설정된 전체 거래량 제한 확인
                if (volume > trade_volume_max || volumeBTC > volumeBTC_Check) {
                    if (TRACE > 0)
                        asyncPrint("Total volume exceeded (F:" + volume + ", S:" + volumeBTC
                                + "), trading not allowed.");
                    TradeFlg = 3; // 필요시 거래 중지
                    spotPriceHistory.clear();
                    futuresPriceHistory.clear();
                    return;
                }

                while (reader.hasNext()) {
                    JsonObject jsonObject = null;
                    try {
                        jsonObject = JsonParser.parseReader(reader).getAsJsonObject();
                    } catch (Exception e) {
                        if (TRACE > 0) {
                            asyncPrint("JSON parsing error in stream: " + e.getMessage());
                        }
                        continue;
                    }

                    if (jsonObject.has("Mtype") && jsonObject.has("message")) {
                        String mtype = jsonObject.get("Mtype").getAsString();
                        String msg = jsonObject.get("message").getAsString();

                        try {
                            // 오더북 및 실시간 데이터 업데이트
                            if ("BTC20".equals(mtype)) { // 스팟 데이터
                                String[] orderbook20MSg = msg.split("-",3);
                                if (orderbook20MSg.length >= 3) {
                                    tradeOrderbookDown20 = Double.parseDouble(orderbook20MSg[0]);
                                    tradeOrderbookUp20 = Double.parseDouble(orderbook20MSg[1]);
                                    tradePrice = Double.parseDouble(orderbook20MSg[2]); // 스팟 현재가
                                    bfirstBTC20 = true;
                                } else {
                                    asyncPrint("Spot data format error: " + msg);
                                }
                            } else if ("FBTC20".equals(mtype)) { // 선물 데이터
                                String[] orderbook20MSg = msg.split("-",3);
                                if (orderbook20MSg.length >= 3) {
                                    tradeOrderbookDownF20 = Double.parseDouble(orderbook20MSg[0]);
                                    tradeOrderbookUpF20 = Double.parseDouble(orderbook20MSg[1]);
                                    tradePriceF = Double.parseDouble(orderbook20MSg[2]); // 선물 현재가
                                    bfirstFBTC20 = true;
                                } else {
                                    asyncPrint("Futures data format error: " + msg);
                                }
                            }
                        } catch (NumberFormatException e) {
                            asyncPrint("Number format error while parsing data: " + msg + " / Error: " + e.getMessage());
                            return; // 잘못된 데이터면 처리 중단
                        } catch (Exception e) {
                            asyncPrint("Exception while processing data: " + msg + " / Error: " + e.getMessage());
                            e.printStackTrace();
                            return;
                        }

                        // 스팟 또는 선물 데이터가 업데이트되었고, 양쪽 데이터가 모두 수신된 상태라면 분석 및 거래 결정 로직 수행
                        if (bfirstBTC20 && bfirstFBTC20) {

                            // 오더북 데이터 유효성 확인
                            if (tradeOrderbookUp20 == 99 || tradeOrderbookDown20 == 99 || tradeOrderbookUpF20 == 99
                                    || tradeOrderbookDownF20 == 99 || tradePrice == -999999 || tradePriceF == -999999) {
                                return;
                            }

                            boolean potentialBuy = false;
                            boolean potentialSell = false;
                            // 두 번째부터는 이전 가격과 정수부 비교
                            // 가격 변동 감지 민감도 향상 (소수부 포함 비교)
                            double priceChangeThreshold = 9; // 임계값 설정
                            if (spotPriceHistory.size() >= MAX_PRICE_HISTORY && futuresPriceHistory.size() >= MAX_PRICE_HISTORY && oldtradePriceF != -999999 && oldtradePrice != -999999) {
                                double spotChange = tradePrice - oldtradePrice;
                                double futuresChange = tradePriceF - oldtradePriceF;
                                                            
                                if (spotChange > priceChangeThreshold || futuresChange > priceChangeThreshold) {
                                    potentialBuy = true;
                                }
                                else if (spotChange < -priceChangeThreshold || futuresChange < -priceChangeThreshold) {
                                    potentialSell = true;
                                }
                            }
                            
                            // 오더북 조건 확인
                            if (potentialBuy && !potentialSell) {
                                executeTradeOnOrderbookEvent(true, Math.min(tradeOrderbookUp20, tradeOrderbookUpF20));
                            } else if (potentialSell && !potentialBuy) {
                                executeTradeOnOrderbookEvent(false, Math.min(tradeOrderbookDown20, tradeOrderbookDownF20));
                            }
                            
                            updatePriceHistory(tradePrice, tradePriceF);
                            
                            oldtradePriceF = tradePriceF;
                            oldtradePrice = tradePrice;

                            if (TRACE > 0) {
                                if(Math.abs(tradePrice - oldtradePrice) > 0 || Math.abs(tradePriceF - oldtradePriceF) > 0) {
                                    asyncPrint(String.format("[TRACE] Price Change - spot: %.4f, futures: %.4f, threshold: %.4f",
                                        tradePrice - oldtradePrice, tradePriceF - oldtradePriceF, priceChangeThreshold));
                                }
                            }
                        }
                    }
                }
            } catch (IOException e) {
                asyncPrint("IOException during JSON stream processing: " + e.getMessage());
            }
        }

        /**
         * 현재 시장 상황 요약 문자열 반환 (로깅용)
         */
        private String getMarketSummary() {
            return " | OB(S U/D): " + roundToTwoDecimalPlaces(tradeOrderbookUp20) + "/" + roundToTwoDecimalPlaces(tradeOrderbookDown20) +
                    " | OB(F U/D): " + roundToTwoDecimalPlaces(tradeOrderbookUpF20) + "/" + roundToTwoDecimalPlaces(tradeOrderbookDownF20) +
                    " | Price(S/F): " + roundToTwoDecimalPlaces(tradePrice) + "/" + roundToTwoDecimalPlaces(tradePriceF) +
                    " | PriceOld(S/F): " + roundToTwoDecimalPlaces(oldtradePrice) + "/" + roundToTwoDecimalPlaces(oldtradePriceF);
        }

        /** 오더북 데이터 초기화 */
        private void resetTradeOrderbook() {
            tradeOrderbookDownF20 = 99;
            tradeOrderbookUpF20 = 99;
            tradeOrderbookDown20 = 99;
            tradeOrderbookUp20 = 99;
            tradePrice = -999999;
            tradePriceF = -999999;
            oldtradePrice=-999999;
            oldtradePriceF=-999999;
        }

        /** 데이터 수신 플래그 초기화 */
        private void resetDataFlags() {
            bfirstBTC20 = false;
            bfirstFBTC20 = false;
        }

        // 오더북 이벤트 감지 및 거래 실행 메서드
        private void executeTradeOnOrderbookEvent(boolean isBuySignal, double orderBookSize) {
        	
            // 거래 가능 상태 확인
            boolean canExecuteTrade = (TradeFlg == 0 ||
                    (isBuySignal && TradeFlg == 2) ||
                    (!isBuySignal && TradeFlg == 1));
                    		
            if (canExecuteTrade && INIT_WAIT > 0) {
            	boolean priceConditionMet = isPriceFluctuationSignificant(isBuySignal);
            	
	            // 거래 실행 (가격 조건 충족 && 반등 위험 없음)
	            if (isBuySignal) {
	                if (priceConditionMet) {
	                	if(TradeFlg == 2)
	                		TradeFlg = 3;
	                	else
                            TradeFlg = 1; // 매수 시도 상태
	                	
		                Trade.INSTANCE.TradeStart(BUY_UP,"");

		                asyncPrint("### Orderbook-based BUY executed ### Orderbook size: " +
		                    roundToTwoDecimalPlaces(orderBookSize) + getMarketSummary());
	                } 
	            } else {
	                if (priceConditionMet) { 
	                	if(TradeFlg == 1)
	                		TradeFlg = 3;
	                	else
                            TradeFlg = 2; // 매도 시도 상태
	                	
		                Trade.INSTANCE.TradeStart(SELL_DOWN,"");

		                asyncPrint("### Orderbook-based SELL executed ### Orderbook size: " +
		                    roundToTwoDecimalPlaces(orderBookSize) + getMarketSummary());
	                } 
	            }
            }
        }
        
        // 가격 이력 업데이트 메서드
        private void updatePriceHistory(double currentSpotPrice, double currentFuturesPrice) {
            // --- 스팟 가격 이력 업데이트 ---
            if (currentSpotPrice > 0) { // 유효한 스팟 가격만 처리
                if (spotPriceHistory.size() < MAX_PRICE_HISTORY) {
                    // 이력이 아직 가득 차지 않았으면
                    if (spotPriceHistory.isEmpty()) {
                        // 첫 번째 데이터는 무조건 추가
                        spotPriceHistory.offer(currentSpotPrice);
                        if (TRACE > 0) { // Changed from TRACE > 1
                            asyncPrint(String.format("Spot price %.4f added (first entry). History size: %d", currentSpotPrice, spotPriceHistory.size()));
                        }
                    } else {
                        // 두 번째부터는 이전 가격과 정수부 비교
                        // ArrayDeque does not have a simple get(index) for the last element without converting to array or iterating.
                        // PeekLast() is suitable here.
                        Double lastHistoricalSpotPrice = spotPriceHistory.peekLast();
                        if (lastHistoricalSpotPrice != null && (int)currentSpotPrice != (int)lastHistoricalSpotPrice.doubleValue()) {
                            spotPriceHistory.offer(currentSpotPrice);
                        }
                    }
                    // 이력이 방금 가득 찼는지 확인하고 로그 (이 위치는 변경 없음)
                    if (TRACE > 0 && spotPriceHistory.size() == MAX_PRICE_HISTORY) { 
                        asyncPrint(String.format("Spot price history now full (%d entries). Update logic will now apply.", MAX_PRICE_HISTORY));
                    }
                } else { // 이력이 이미 가득 찬 경우 (spotPriceHistory.size() == MAX_PRICE_HISTORY)
                    Double lastHistoricalSpotPrice = spotPriceHistory.peekLast();
                    if (lastHistoricalSpotPrice != null && (int)currentSpotPrice != (int)lastHistoricalSpotPrice.doubleValue()) {
                        spotPriceHistory.poll(); 
                        spotPriceHistory.offer(currentSpotPrice); 
                    }
                }
            }

            // --- 선물 가격 이력 업데이트 (스팟과 동일한 통합 로직 적용) ---
            if (currentFuturesPrice > 0) { // 유효한 선물 가격만 처리
                if (futuresPriceHistory.size() < MAX_PRICE_HISTORY) {
                    if (futuresPriceHistory.isEmpty()) {
                        futuresPriceHistory.offer(currentFuturesPrice);
                        if (TRACE > 0) { // Changed from TRACE > 1
                            asyncPrint(String.format("Futures price %.4f added (first entry). History size: %d", currentFuturesPrice, futuresPriceHistory.size()));
                        }
                    } else {
                        Double lastHistoricalFuturesPrice = futuresPriceHistory.peekLast();
                        if (lastHistoricalFuturesPrice != null && (int)currentFuturesPrice != (int)lastHistoricalFuturesPrice.doubleValue()) {
                            futuresPriceHistory.offer(currentFuturesPrice);
                        }
                    }
                    if (TRACE > 0 && futuresPriceHistory.size() == MAX_PRICE_HISTORY) { 
                        asyncPrint(String.format("Futures price history now full (%d entries). Update logic will now apply.", MAX_PRICE_HISTORY));
                    }
                } else { // 이력이 이미 가득 찬 경우
                    Double lastHistoricalFuturesPrice = futuresPriceHistory.peekLast();
                    if (lastHistoricalFuturesPrice != null && (int)currentFuturesPrice != (int)lastHistoricalFuturesPrice.doubleValue()) {
                        futuresPriceHistory.poll(); 
                        futuresPriceHistory.offer(currentFuturesPrice); 
                    }
                }
            }
        }

        // 가격 변동이 유의미한지 확인하는 메서드 (이전 가격과의 차이 평균 기반)
        private boolean isPriceFluctuationSignificant(boolean isBuySignal) {
            // MAX_PRICE_HISTORY 만큼 가격 정보가 쌓인 후에만 조건을 평가
            if (spotPriceHistory.size() < MAX_PRICE_HISTORY || futuresPriceHistory.size() < MAX_PRICE_HISTORY) {
                if (TRACE > 0) { // 상세 로그 추가
                    asyncPrint(String.format("PriceFluctuation: Skipped. History not full. Spot: %d/%d, Futures: %d/%d",
                        spotPriceHistory.size(), MAX_PRICE_HISTORY,
                        futuresPriceHistory.size(), MAX_PRICE_HISTORY));
                }
                return false; // 가격 정보가 충분히 쌓이지 않았으면 조건 미충족
            }

            if (oldtradePrice == -999999 || oldtradePriceF == -999999) {
                if (TRACE > 0) asyncPrint("Initial prices not set. Skipping fluctuation check.");
                return false;
            }

            double avgAbsSpotDiff = (int)getAverageAbsoluteHistoricalDifference(spotPriceHistory);
            double avgAbsFuturesDiff = (int)getAverageAbsoluteHistoricalDifference(futuresPriceHistory);
            double currentSpotDiff = (int)(tradePrice - oldtradePrice);
            double currentFuturesDiff = (int)(tradePriceF -oldtradePriceF);
            
            if(avgAbsSpotDiff < 2)
            	avgAbsSpotDiff = 2;
            
            if(avgAbsFuturesDiff < 2)
            	avgAbsFuturesDiff = 2;

            if (TRACE > 0) {
                StringBuilder sb = new StringBuilder();
                sb.append(String.format("PriceFluctuation Check (isBuySignal: %b):", isBuySignal)).append("\n")
                  .append(String.format("  Spot: avgAbsDiff=%.4f, currentDiff=%.4f", avgAbsSpotDiff, currentSpotDiff)).append("\n")
                  .append(String.format("  Futures: avgAbsDiff=%.4f, currentDiff=%.4f", avgAbsFuturesDiff, currentFuturesDiff));
                asyncPrint(sb.toString());
            }

            int isMarketStable = 5;  
            
            boolean spotConditionMet = false;
            if (avgAbsSpotDiff < 1e-9) { 
                if (TRACE > 0) asyncPrint("  Spot: Using near-zero avgAbsDiff path.");
                if (isBuySignal && currentSpotDiff > 1e-9) { 
                    spotConditionMet = false;
                } else if (!isBuySignal && currentSpotDiff < -1e-9) { 
                    spotConditionMet = false;
                }
            } else { 
                if (TRACE > 0) asyncPrint(String.format("  Spot: Using %dx avgAbsDiff path (threshold: %.4f).", isMarketStable, isMarketStable * avgAbsSpotDiff));
                if (isBuySignal && currentSpotDiff > 0 && currentSpotDiff > isMarketStable * avgAbsSpotDiff) {
                    spotConditionMet = true;
                } else if (!isBuySignal && currentSpotDiff < 0 && Math.abs(currentSpotDiff) > isMarketStable * avgAbsSpotDiff) {
                    spotConditionMet = true;
                }
            }
            if (TRACE > 0) asyncPrint(String.format("  SpotConditionMet: %b", spotConditionMet));

            boolean futuresConditionMet = false;
            if (avgAbsFuturesDiff < 1e-9) {
                if (TRACE > 0) asyncPrint("  Futures: Using near-zero avgAbsDiff path.");
                if (isBuySignal && currentFuturesDiff > 1e-9) {
                    futuresConditionMet = false;
                } else if (!isBuySignal && currentFuturesDiff < -1e-9) {
                    futuresConditionMet = false;
                }
            } else {
                if (TRACE > 0) asyncPrint(String.format("  Futures: Using %dx avgAbsDiff path (threshold: %.4f).", isMarketStable, isMarketStable * avgAbsFuturesDiff));
                if (isBuySignal && currentFuturesDiff > 0 && currentFuturesDiff > isMarketStable * avgAbsFuturesDiff) {
                    futuresConditionMet = true;
                } else if (!isBuySignal && currentFuturesDiff < 0 && Math.abs(currentFuturesDiff) > isMarketStable * avgAbsFuturesDiff) {
                    futuresConditionMet = true;
                }
            }
            if (TRACE > 0) asyncPrint(String.format("  FuturesConditionMet: %b", futuresConditionMet));
            
            // This existing log provides a good summary if a condition is met.
            if (TRACE > 0 && (spotConditionMet || futuresConditionMet)) { 
                 asyncPrint(String.format("PriceFluctuation: DETECTED isBuy=%b, spotMet=%b (curD:%.4f, avgAbsD:%.4f), futMet=%b (curD:%.4f, avgAbsD:%.4f)",
                    isBuySignal, spotConditionMet, currentSpotDiff, avgAbsSpotDiff,
                    futuresConditionMet, currentFuturesDiff, avgAbsFuturesDiff
                 ));
            } else if (TRACE > 0) {
                asyncPrint("PriceFluctuation: NOT detected.");
            }

            return spotConditionMet || futuresConditionMet;
        }


        // 과거 가격 변동의 절대값 평균을 계산하는 헬퍼 메서드
        // (변동이 0인 경우는 평균 계산에서 제외)
        private double getAverageAbsoluteHistoricalDifference(Deque<Double> priceHistoryDeque) {
            List<Double> priceHistory = new ArrayList<>(priceHistoryDeque);
            int numHistoricalPricePoints = priceHistory.size();
            // 최소 2개의 가격 데이터가 있어야 의미 있는 차이를 계산할 수 있습니다.
            if (numHistoricalPricePoints < 2) {
                return 0.0; // 충분한 데이터가 없으면 0 반환
            }

            double sumAbsoluteNonZeroDifferences = 0.0;
            int countNonZeroDifferences = 0;

            // 모든 연속된 가격 쌍의 차이를 계산합니다 (마지막 데이터까지 포함)
            // 예를 들어, 가격 이력이 [P0, P1, P2, P3, P4] (size=5) 라면,
            // 루프는 P0,P1 -> P1,P2 -> P2,P3 -> P3,P4의 모든 차이를 계산합니다.
            for (int i = 0; i < numHistoricalPricePoints - 1; i++) {
                double previousPrice = priceHistory.get(i);
                double currentPrice = priceHistory.get(i + 1);
                double absoluteDifference = Math.abs(currentPrice - previousPrice);

                // 변동이 0이 아닌 경우에만 합계 및 카운트에 포함
                if (absoluteDifference > 1e-9) { // 1e-9는 매우 작은 값 (0에 가까움)
                    sumAbsoluteNonZeroDifferences += absoluteDifference;
                    countNonZeroDifferences++;
                }
            }

            if (countNonZeroDifferences == 0) {
                return 0.0; // 모든 과거 변동이 0이었음
            } else {
                return sumAbsoluteNonZeroDifferences / countNonZeroDifferences;
            }
        }
    } // End of MessageHandler class
    
 
    public static void InitMain() {
        try {
            if (REFRESH_CNT > 20) {
                Trade.INSTANCE.TradeStart(REFRESH,"");
                REFRESH_CNT = 0;
                Trade.INSTANCE.TradeStart(TIME_RESET,"");
                Thread.sleep(10000);
            }
            REFRESH_CNT++;
            // 1. 전체 거래량 계산 (최근 10개 1분봉 기준 평균)
            volume = 0; // 선물
            volumeBTC = 0; // 스팟
            List<Candlestick> candlestickListF = clientF.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 20,
                    null, null);
            List<Candlestick> candlestickListS = client.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 20,
                    null, null);
            Candlestick candlestickF = null;
            int countF = 0;
            if (candlestickListF != null && !candlestickListF.isEmpty()) {
                for (int i = 0; i < candlestickListF.size(); i++) {
                    candlestickF = candlestickListF.get(i);
                    // 마지막 캔들 제외, 최근 10개 캔들 (인덱스 9~18)
                    if (i >= candlestickListF.size() - 6 && i < candlestickListF.size() - 1) {
                        volume += Double.parseDouble(candlestickF.getVolume());
                        countF++;
                    }
                }
            }
            if (countF > 0)
                volume = volume / countF;
            volume = roundToTwoDecimalPlaces(volume);

            Candlestick candlestickS = null;
            int countS = 0;
            List<Double> priceListS = new ArrayList<>();
            if (candlestickListS != null && !candlestickListS.isEmpty()) {
                for (int i = 0; i < candlestickListS.size(); i++) {
                    candlestickS = candlestickListS.get(i);
                    priceListS.add(Double.parseDouble(candlestickS.getClose()));
                    if (i >= candlestickListS.size() - 6 && i < candlestickListS.size() - 1) {
                        volumeBTC += Double.parseDouble(candlestickS.getVolume());
                        countS++;
                    }
                }
            }
            if (countS > 0)
                volumeBTC = volumeBTC / countS;
            volumeBTC = roundToTwoDecimalPlaces(volumeBTC);

            // 스팟 볼린저 밴드 계산 및 이탈 확인
            BollCheck = 0; // 기본값 초기화
            if (!priceListS.isEmpty()) {
                BollingerBands bollS = new BollingerBands(priceListS, 20, 2.0);
                BollDown = bollS.getdownBollingerBands();
                BollUp = bollS.getUpBollingerBands();

                // 마지막 스팟 캔들 기준 이탈 확인 및 거리 계산
                if (!candlestickListS.isEmpty()) {
                    Candlestick lastCandlestickS = candlestickListS.get(candlestickListS.size() - 1);                    
                    if (BollUp > 0 && BollDown > 0) { // 유효한 볼린저 밴드 값일 때만 체크
                        double closePrice = Double.parseDouble(lastCandlestickS.getClose());
                        if (closePrice > BollUp || closePrice < BollDown) {
                            BollCheck = 1;
                        }
                    }
                }
            } else {
                BollUp = 0;
                BollDown = 0;
            }

            double rsiValue = 50; // 기본값 50

            // ATR 계산: 표준 ATR 계산 (14기간)
            double atrValue = 0.005; // 기본값 초기화
            if (candlestickListS != null && candlestickListS.size() >= 15) {
                List<Double> trueRanges = new ArrayList<>();
                
                for (int i = 1; i < candlestickListS.size(); i++) {
                    Candlestick current = candlestickListS.get(i);
                    Candlestick previous = candlestickListS.get(i-1);
                    
                    double high = Double.parseDouble(current.getHigh());
                    double low = Double.parseDouble(current.getLow());
                    double prevClose = Double.parseDouble(previous.getClose());
                    
                    double tr1 = high - low;
                    double tr2 = Math.abs(high - prevClose);
                    double tr3 = Math.abs(low - prevClose);
                    double tr = Math.max(tr1, Math.max(tr2, tr3));
                    trueRanges.add(tr);
                }
                
                // 최근 14개 TR 평균 계산
                int startIdx = Math.max(0, trueRanges.size() - 14);
                double sum = 0;
                int count = 0;
                for (int i = startIdx; i < trueRanges.size(); i++) {
                    sum += trueRanges.get(i);
                    count++;
                }
                
                if (count > 0) {
                    atrValue = sum / count;
                    atrValue /= priceListS.getLast(); // 상대 ATR 변환
                }
            }
            
            // RSI 계산 (임시 구현)
            // TODO: 실제 RSI 계산 알고리즘 구현 필요
            if (priceListS.size() >= 14) {
                // 간단한 RSI 계산 예시 (실제 구현은 별도 메서드로)
                double gains = 0;
                double losses = 0;
                for (int i = priceListS.size() - 14; i < priceListS.size() - 1; i++) {
                    double change = priceListS.get(i+1) - priceListS.get(i);
                    if (change > 0) gains += change;
                    else losses -= change;
                }
                double rs = gains / losses;
                rsiValue = 100 - (100 / (1 + rs));
            } else {
                rsiValue = 50; // 기본값
            }

            // 2. 설정 파일 로드
            String filePath = "c:/Trade/PrimeOption.properties";
            try (InputStream input = new FileInputStream(filePath)) {
                Properties prop = new Properties();
                prop.load(input);

                volumeBTC_Check = Double.parseDouble(prop.getProperty("volumeBTC_Check", "15"));
                trade_volume_max = Integer.parseInt(prop.getProperty("trade_volume_max", "15"));
                checkboll = Integer.parseInt(prop.getProperty("checkboll", "0")); // 볼린저밴드 체크 활성화
                INIT_WAIT = Integer.parseInt(prop.getProperty("INIT_WAIT", "0"));
                TRACE = Integer.parseInt(prop.getProperty("TRACE", "0")); // 로그 레벨
                MONEY = prop.getProperty("MONEY", "2"); // 로그 레벨
                
            } catch (IOException | NumberFormatException e) {
                asyncPrint("Error loading or parsing configuration file: " + e.getMessage());
                // 기본값 유지 또는 오류 처리
            }

            // 로그 출력 (TRACE 레벨에 따라)
            asyncPrint("--- Initial Settings and Status ---");
            asyncPrint("Total Volume Limits (S/F): " + volumeBTC_Check + "/" + trade_volume_max);
            asyncPrint("Current Average Volume (S/F): " + volumeBTC + "/" + volume);
            asyncPrint("Bollinger Bands (Lower/Upper/Check/Enabled): " + roundToTwoDecimalPlaces(BollDown) + "/"
                + roundToTwoDecimalPlaces(BollUp) + "/" + BollCheck + "/" + checkboll);
            asyncPrint("MONEY: " + MONEY);
            asyncPrint("Other (INIT_WAIT/TRACE): " + INIT_WAIT + "/" + TRACE);
            asyncPrint("ATR: " + roundToTwoDecimalPlaces(atrValue * 100) + "%");
            asyncPrint("RSI: " + roundToTwoDecimalPlaces(rsiValue));
            if(BollCheck == 1 || atrValue > 0.0035 || rsiValue < 25 || rsiValue > 75) {
                asyncPrint("Warning: ATR or RSI values indicate high volatility or overbought/oversold conditions.");
                INIT_WAIT = 0; // 초기화 대기 시간 해제
            }
            asyncPrint("--------------------------");

            Trade.INSTANCE.TradeStart(SETMONEY,MONEY); 

        } catch (Exception e) { // Binance API 호출 등에서 발생 가능
            asyncPrint("Error during InitMain execution: " + e.getMessage());
            e.printStackTrace();
            volume = 0; // 오류 시 초기화
            volumeBTC = 0;
            BollCheck = 0;
        }
        
    }

    public static void main(String[] args) throws InterruptedException, IOException {
        // 바이낸스 클라이언트 생성 (API 키 필요 시 입력)
        BinancefactoryF = BinanceAbstractFactory.createFuturesFactory("", "");
        clientF = BinancefactoryF.newRestClient();

        factory = BinanceAbstractFactory.createSpotFactory("", "");
        client = factory.newRestClient();

        // 트레이딩 UI 실행 (추정)
        Trade.INSTANCE.TradeBitqDlg_expert();
        Trade.INSTANCE.TradeStart(WINDOWS_TOP,""); // 창 상단 고정?
        Trade.INSTANCE.TradeStart(TIME_RESET,""); // 시간 리셋?

        asyncPrint("Waiting for initialization (10 seconds)...");
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            e.printStackTrace();
        }
        asyncPrint("Initialization wait complete.");

        // 메시지 핸들러 인스턴스 생성 및 스레드 시작
        MessageHandler messageHandlerInstance = new MessageHandler(); // Create instance first
        Thread messageHandlerThread = new Thread(messageHandlerInstance); // Pass instance to thread constructor
        messageHandlerThread.setName("MessageHandlerThread");
        messageHandlerThread.start();
        asyncPrint("MessageHandler thread started.");

        asyncPrint("Main thread waiting started.");
        // 메인 스레드는 무한 대기 또는 다른 작업 수행
        while (true) {
            try {
                Thread.sleep(60000); // 1분마다 상태 출력 또는 확인
                // asyncPrint("메인 스레드 대기 중...");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                asyncPrint("Main thread interrupted. Shutting down...");
                // 필요시 하위 스레드 종료 로직 추가
                messageProcessingService.shutdownNow(); // 즉시 종료 시도
                PrintService.shutdownNow();

                // Wait for termination with timeout
                awaitTermination(messageProcessingService, "messageProcessingService");
                awaitTermination(PrintService, "PrintService");

                messageHandlerThread.interrupt(); // Interrupt the main handler thread
                // messageHandler1Thread.interrupt(); // Uncomment if MessageHandler1 is used
                break;
            }
        }
        asyncPrint("Main thread terminated.");
    }

    // Helper method for executor termination
    private static void awaitTermination(ExecutorService executor, String name) {
        try {
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                System.err.println(name + " did not terminate gracefully after shutdown(). Forcing shutdownNow().");
                List<Runnable> droppedTasks = executor.shutdownNow();
                System.err.println(name + " dropped " + droppedTasks.size() + " tasks.");
                // Wait a little longer for tasks to respond to cancellation
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    System.err.println(name + " did not terminate even after shutdownNow().");
                } else {
                    System.out.println(name + " terminated after shutdownNow().");
                }
            } else {
                System.out.println(name + " terminated gracefully.");
            }
        } catch (InterruptedException e) {
            System.err.println("Interrupted while waiting for " + name + " termination. Forcing shutdownNow().");
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    private static double roundToTwoDecimalPlaces(double value) {
        // 소수점 4자리까지 반올림 (가격 등에 사용될 수 있으므로 정밀도 유지)
        DecimalFormat df_local = new DecimalFormat("#.####");
        // DecimalFormat은 스레드 안전하지 않으므로 지역 변수로 사용하거나 동기화 필요
        // 여기서는 지역 변수로 사용
        try {
            return Double.parseDouble(df_local.format(value));
        } catch (NumberFormatException e) {
            return value; // 오류 시 원본 값 반환
        }
    }
    
}
