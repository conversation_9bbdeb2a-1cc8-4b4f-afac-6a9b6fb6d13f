package com.gettrade.start;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.Socket;
import java.text.DecimalFormat;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import com.binance.api.client.api.sync.BinanceApiFuturesRestClient;
import com.binance.api.client.api.sync.BinanceApiSpotRestClient;
import com.binance.api.client.domain.market.Candlestick;
import com.binance.api.client.domain.market.CandlestickInterval;
import com.binance.api.client.factory.BinanceAbstractFactory;
import com.binance.api.client.factory.BinanceFuturesApiClientFactory;
import com.binance.api.client.factory.BinanceSpotApiClientFactory;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.stream.JsonReader;
import com.sun.jna.Library;
import com.sun.jna.Native;

public class BCGAME_Binary {

    public enum TradeSignal {
        UP, DOWN, HOLD
    }

    public enum TradeStatus {
        WAITING, // 거래 대기
        TRADING, // 거래 진행 중
        STOPPED_BY_VOLUME, // 거래량으로 인한 거래 중지
        STOPPED_BY_CYCLE,  // UP/DOWN 1회 완료 후 중지
        STOPPED_MANUALLY // 수동 중지
    }

    public interface Trade extends Library {
        Trade INSTANCE = (Trade) Native.loadLibrary("MTrade", Trade.class);

        public void TradeBitqDlg_expert();
        public int GetUpDownSecRcv();
        public void TradeStart(int nflag, String value);
    }

    // 거래 상수
    public static int BUY_UP = 100004;
    public static int SELL_DOWN = 100005;
    public static int CHECK = 500203;
    public static int SETMONEY = 500204;
    public static int WINDOWS_TOP = 300008;
    public static int TIME_RESET = 4;
    public static int REFRESH = 304;
    public static int TRADE_STOP = 103;
    public static int REFRESH_CNT = 0;
    
    // 설정 및 상태 변수
    public static double volume = 0; // 전체 퓨처 거래량 (과거 데이터 기반)
    public static double volumeBTC = 0; // 전체 스팟 거래량 (과거 데이터 기반)
    public static double volumeBTC_Check = 15; // 스팟 거래량 임계값 (설정값)
    public static double trade_volume_max = 15; // 퓨처 거래량 임계값 (설정값)
    public static double BollUp = 0.0;
    public static double BollDown = 0.0;
    public static volatile TradeStatus tradeStatus = TradeStatus.WAITING;
    public static int INIT_WAIT = 0; // 초기 대기 시간 관련 플래그 (설정값)
    public static int TRACE = 0; // 디버깅 로그 레벨 (설정값)
    public static String MONEY = "2";

    // 사이클 관리 변수
    private static AtomicBoolean hasTradedUpInCycle = new AtomicBoolean(false);
    private static AtomicBoolean hasTradedDownInCycle = new AtomicBoolean(false);

    // RSI 계산기를 정적 변수로 유지하여 상태 보존
    private static RSI rsi14Calculator = new RSI(14);
    
    // 지표 공유 변수
    public static volatile double currentRSI = 50.0;
    public static volatile double currentUpperBB = Double.NaN;
    public static volatile double currentLowerBB = Double.NaN;
    public static volatile double currentMiddleBB = Double.NaN; 

    // 바이낸스 클라이언트
    public static BinanceFuturesApiClientFactory BinancefactoryF = null;
    public static BinanceApiFuturesRestClient clientF = null;
    public static BinanceSpotApiClientFactory factory = null;
    public static BinanceApiSpotRestClient client = null;
    private static AtomicBoolean initTrade = new AtomicBoolean(false); // 초기화 진행 중 플래그
    private static AtomicBoolean first = new AtomicBoolean(false); // 첫 메시지 수신 여부 플래그

    public static String symbol = "BTCUSDT";
    private static final ExecutorService messageProcessingService = Executors.newCachedThreadPool();
    private static final ExecutorService PrintService = Executors.newSingleThreadExecutor();

    public static void asyncPrint(String message) {
        PrintService.submit(() -> System.out.println(message));
    }

    // --- 지표 계산 클래스들 (RSI, BollingerBands) ---
    public static class RSI {
        private final int period;
        private Double avgGain = null;
        private Double avgLoss = null;
        private Double previousPrice = null;

        public RSI(int period) {
            this.period = period;
        }

        public double calculate(List<Double> prices) {
            if (prices.size() < period + 1) {
                return 50.0;
            }

            if (avgGain == null) { // 첫 계산 - SMA 방식으로 초기값 계산
                double totalGain = 0;
                double totalLoss = 0;
                
                for (int i = 1; i <= period; i++) {
                    double change = prices.get(i) - prices.get(i - 1);
                    if (change > 0) {
                        totalGain += change;
                    } else {
                        totalLoss += Math.abs(change);
                    }
                }
                
                avgGain = totalGain / period;
                avgLoss = totalLoss / period;
                previousPrice = prices.get(period);
            } else { // Wilder's Smoothing 적용
                double currentPrice = prices.get(prices.size() - 1);
                double change = currentPrice - previousPrice;
                
                double gain = Math.max(change, 0);
                double loss = Math.max(-change, 0);
                
                // Wilder's Smoothing: α = 1/period
                double alpha = 1.0 / period;
                avgGain = (1 - alpha) * avgGain + alpha * gain;
                avgLoss = (1 - alpha) * avgLoss + alpha * loss;
                
                previousPrice = currentPrice;
            }

            if (avgLoss == 0) {
                return 100.0;
            }
            
            double rs = avgGain / avgLoss;
            return 100 - (100 / (1 + rs));
        }
    }
    
    // 볼린져 밴드 클래스
    public static class BollingerBands {
        private double upperBand;
        private double lowerBand;
        private double middleBand;

        public BollingerBands(List<Double> prices, int period, double stdDevFactor) {
            if (prices.size() >= period) {
                List<Double> subset = prices.subList(prices.size() - period, prices.size());
                calculateBollingerBands(subset, stdDevFactor);
            }
        }

        private void calculateBollingerBands(List<Double> prices, double stdDevFactor) {
            double sma = calculateSMA(prices);
            double stdDev = calculateStdDev(prices, sma);

            this.upperBand = sma + stdDevFactor * stdDev;
            this.lowerBand = sma - stdDevFactor * stdDev;
            this.middleBand = sma;
        }

        private double calculateSMA(List<Double> prices) {
            double sum = 0.0;
            for (double price : prices) {
                sum += price;
            }
            return sum / prices.size();
        }

        private double calculateStdDev(List<Double> prices, double sma) {
            double sum = 0.0;
            for (double price : prices) {
                sum += Math.pow(price - sma, 2.0);
            }
            // 볼린저 밴드에서는 모집단 표준편차 사용 (N으로 나눔)
            double variance = sum / prices.size();
            return Math.sqrt(variance);
        }
        
        public double getUpBollingerBands() {
            return upperBand;
        }
        
        public double getdownBollingerBands() {
            return lowerBand;
        }
        
        public double getMidBollingerBands() {
            return middleBand;
        }
    }

    // --- MessageHandler (Main Trading Logic) ---
    static class MessageHandler implements Runnable {
        // 오더북 및 가격 데이터
        private volatile double tradeOrderbookDownF20 = 99;
        private volatile double tradeOrderbookUpF20 = 99;
        private volatile double tradeOrderbookDown20 = 99;
        private volatile double tradeOrderbookUp20 = 99;
        private volatile double tradePriceF = -999999; // 선물 현재가
        private volatile double tradePrice = -999999; // 스팟 현재가

        // 데이터 수신 플래그
        private volatile boolean bfirstBTC20 = false;
        private volatile boolean bfirstFBTC20 = false;

        private int lastInitSecond = -1; // 5초 주기 실행을 제어하기 위한 플래그

        @Override
        public void run() {
            while (true) {
                try (Socket socket = new Socket("192.168.11.14", 22222)) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                    asyncPrint("Data receiving socket connection successful.");
                    resetDataFlags(); // 연결 시 플래그 초기화

                    while (true) {
                        String message = in.readLine();
                        if (message != null && !message.isEmpty()) {
                            long totalMilliseconds = System.currentTimeMillis();
                            long currentSecond = (totalMilliseconds / 1000) % 60;

                            // 매 분 59초 또는 첫 실행 시 초기화 작업 수행
                            if ((currentSecond == 59 || !first.get())) {
                                if (initTrade.compareAndSet(false, true)) {
                                    messageProcessingService.submit(() -> {
                                        try {
                                            //Trade.INSTANCE.TradeStart(CHECK,""); // 상태 체크
                                            BCGAME_Binary.InitMain(); // 설정값 및 볼린저 밴드 등 업데이트
                                            resetTradeOrderbook(); // 오더북 데이터 초기화
                                            resetDataFlags(); // 데이터 수신 플래그 초기화 -> 연결 시 한 번만 수행하도록 변경
                                            if (tradeStatus == TradeStatus.STOPPED_BY_CYCLE)
                                                tradeStatus = TradeStatus.WAITING; // 거래 중지 상태 해제
                                            asyncPrint("Regular initialization completed (tradeStatus: "
                                                    + tradeStatus + ")");
                                            Thread.sleep(1000);
                                            //if (Trade.INSTANCE.GetUpDownSecRcv() != 1) {
                                            //    INIT_WAIT = 0; // 설정 파일 반영
                                            //    asyncPrint("INIT_WAIT setting applied: " + INIT_WAIT);
                                            //}
                                            lastInitSecond = (int) currentSecond; // 마지막 실행 시간 기록
                                        } catch (Exception e) {
                                            asyncPrint("Error during initialization: " + e.getMessage());
                                            e.printStackTrace();
                                        } finally {
                                            first.set(true); // 첫 실행 완료 플래그 설정
                                            initTrade.set(false);
                                        }
                                    });
                                }
                            }// 매 5초마다 데이터 업데이트 (59초 제외)
                            else if (currentSecond % 5 == 0 && currentSecond != 59 && lastInitSecond != currentSecond) {
                                if (initTrade.compareAndSet(false, true)) {
                                    lastInitSecond = (int) currentSecond; // 실행 시간 기록
                                    messageProcessingService.submit(() -> {
                                        try {
                                            InitMain(); 
                                            resetDataFlags();
                                            if (TRACE > 1) asyncPrint("Periodic 5-second data update completed.");
                                        } catch (Exception e) {
                                            asyncPrint("Error during periodic 5s reset: " + e.getMessage());
                                        } finally {
                                            initTrade.set(false);
                                        }
                                    });
                                }
                            }
                            // 그 외 시간에는 메시지 처리
                            else if (first.get()) {
                                processMessage(message);
                            }
                        } 
                    }
                } catch (IOException e) {
                    asyncPrint("Socket connection error or data receiving error: " + e.getMessage());
                    resetTradeOrderbook();
                    resetDataFlags();
                    initTrade.set(false); // 작업 중 플래그 해제
                    first.set(false); // 첫 실행 플래그 리셋
                    try {
                        Thread.sleep(5000); // 5초 후 재연결 시도
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        asyncPrint("Interrupt occurred while waiting for reconnection.");
                    }
                } catch (Exception ex) {
                    asyncPrint("Unexpected error in MessageHandler loop: " + ex.getMessage());
                    ex.printStackTrace();
                    initTrade.set(false);
                    try {
                        Thread.sleep(1000); // 잠시 대기
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }

        /**
         * 수신된 메시지를 처리하고 거래 조건을 확인하여 거래를 실행합니다.
         */
        private void processMessage(String message) {

            if (message == null || message.isEmpty()) {
                asyncPrint("Empty message received, skipping processing.");
                return;
            }
            
            try (JsonReader reader = new JsonReader(new StringReader(message))) {
                reader.setLenient(true);

                // asyncPrint("메시지 수신: " + message); // 디버깅 시 활성화
                if (tradeStatus != TradeStatus.WAITING) { // 거래 중지 상태이면 처리 안 함
                    return;
                }

                // 설정된 전체 거래량 제한 확인 (둘 중 하나라도 만족하면 거래 가능)
                boolean volumeConditionMet = (volume < trade_volume_max) || (volumeBTC < volumeBTC_Check);
                if (!volumeConditionMet) {
                    if (TRACE > 0)
                        asyncPrint("Total volume exceeded (F:" + volume + ", S:" + volumeBTC
                                + "), trading not allowed.");
                    tradeStatus = TradeStatus.STOPPED_BY_VOLUME; // 거래 중지
                    return;
                }

                while (reader.hasNext()) {
                    JsonObject jsonObject = null;
                    try {
                        jsonObject = JsonParser.parseReader(reader).getAsJsonObject();
                    } catch (Exception e) {
                        if (TRACE > 0) {
                            asyncPrint("JSON parsing error in stream: " + e.getMessage());
                        }
                        continue;
                    }

                    if (jsonObject.has("Mtype") && jsonObject.has("message")) {
                        String mtype = jsonObject.get("Mtype").getAsString();
                        String msg = jsonObject.get("message").getAsString();

                        try {
                            // 오더북 및 실시간 데이터 업데이트
                            if ("BTC20".equals(mtype)) { // 스팟 데이터
                                String[] orderbook20MSg = msg.split("-",3);
                                if (orderbook20MSg.length >= 3) {
                                    tradeOrderbookDown20 = Double.parseDouble(orderbook20MSg[0]);
                                    tradeOrderbookUp20 = Double.parseDouble(orderbook20MSg[1]);
                                    tradePrice = Double.parseDouble(orderbook20MSg[2]); // 스팟 현재가
                                    bfirstBTC20 = true;
                                } else {
                                    asyncPrint("Spot data format error: " + msg);
                                }
                            } else if ("FBTC20".equals(mtype)) { // 선물 데이터
                                String[] orderbook20MSg = msg.split("-",3);
                                if (orderbook20MSg.length >= 3) {
                                    tradeOrderbookDownF20 = Double.parseDouble(orderbook20MSg[0]);
                                    tradeOrderbookUpF20 = Double.parseDouble(orderbook20MSg[1]);
                                    tradePriceF = Double.parseDouble(orderbook20MSg[2]); // 선물 현재가
                                    bfirstFBTC20 = true;
                                } else {
                                    asyncPrint("Futures data format error: " + msg);
                                }
                            }
                        } catch (NumberFormatException e) {
                            asyncPrint("Number format error while parsing data: " + msg + " / Error: " + e.getMessage());
                            return; // 잘못된 데이터면 처리 중단
                        } catch (Exception e) {
                            asyncPrint("Exception while processing data: " + msg + " / Error: " + e.getMessage());
                            e.printStackTrace();
                            return;
                        }

                            // 스팟 또는 선물 데이터가 업데이트되었고, 양쪽 데이터가 모두 수신된 상태라면 분석 및 거래 결정 로직 수행
                            if (bfirstBTC20 && bfirstFBTC20) {

                                // 오더북 데이터 유효성 확인
                                if (tradeOrderbookUp20 == 99 || tradeOrderbookDown20 == 99 || tradeOrderbookUpF20 == 99
                                        || tradeOrderbookDownF20 == 99 || tradePrice == -999999 || tradePriceF == -999999) {
                                    return;
                                }

                                // 거래 상태가 WAITING인 경우에만 거래 신호를 확인
                                if (tradeStatus == TradeStatus.WAITING) {
                                    TradeSignal signal = determineTradeAction();
                                    if (signal != TradeSignal.HOLD) {
                                        executeTrade(signal);
                                    }
                                }
                            }
                    }
                }
            } catch (IOException e) {
                asyncPrint("IOException during JSON stream processing: " + e.getMessage());
            }
        }

        /** 오더북 데이터 초기화 */
        private void resetTradeOrderbook() {
            tradeOrderbookDownF20 = 99;
            tradeOrderbookUpF20 = 99;
            tradeOrderbookDown20 = 99;
            tradeOrderbookUp20 = 99;
            tradePrice = -999999;
            tradePriceF = -999999;
        }

        /** 데이터 수신 플래그 초기화 */
        private void resetDataFlags() {
            bfirstBTC20 = false;
            bfirstFBTC20 = false;
        }

        // 오더북 이벤트 감지 및 거래 실행 메서드
        /**
         * expertoption.java 스타일의 거래 신호 결정 로직
         */
        private TradeSignal determineTradeAction() {
            // 1. 기본 오더북 거래 신호 체크
            boolean basicBuySignal = tradeOrderbookUp20 < 1 || tradeOrderbookUpF20 < 1;
            boolean basicSellSignal = tradeOrderbookDown20 < 1 || tradeOrderbookDownF20 < 1;
            
            // 2. 기본 신호가 없으면 HOLD
            if ((!basicBuySignal && !basicSellSignal) || (basicBuySignal && basicSellSignal)) {
                return TradeSignal.HOLD;
            }
            
            // 3. 거래량 제한 체크 (둘 중 하나라도 만족하면 거래 가능)
            boolean volumeConditionMet = (volume < trade_volume_max) || (volumeBTC < volumeBTC_Check);
            if (!volumeConditionMet) {
                if (TRACE > 0) {
                    asyncPrint(String.format("Volume condition not met - Futures: %.2f/%.2f, Spot: %.2f/%.2f", 
                        volume, trade_volume_max, volumeBTC, volumeBTC_Check));
                }
                return TradeSignal.HOLD;
            }
            
            // 4. 기술적 분석 필터 적용
            TradeSignal filteredSignal = applyTechnicalFilters(basicBuySignal, basicSellSignal);
            
            if (filteredSignal != TradeSignal.HOLD && TRACE > 0) {
                asyncPrint(String.format("### %s Signal ### RSI:%.2f, BB(U/M/L):%.2f/%.2f/%.2f, Price:%.2f", 
                    filteredSignal, currentRSI, currentUpperBB, currentMiddleBB, currentLowerBB, tradePrice));
            }
            
            return filteredSignal;
        }
        
        /**
         * 기술적 분석 필터 적용
         */
        private TradeSignal applyTechnicalFilters(boolean basicBuySignal, boolean basicSellSignal) {
            double currentPrice = tradePrice;
            
            // 볼린져 밴드와 RSI가 계산되지 않은 경우 기본 로직 사용
            if (Double.isNaN(currentUpperBB) || Double.isNaN(currentLowerBB) || currentRSI == 50.0) {
                if (TRACE > 1) asyncPrint("Technical indicators not ready, using basic orderbook signals");
                return TradeSignal.HOLD;
            }
            
            // 현재 가격의 볼린져 밴드 내 위치 계산
            double bbPosition = (currentPrice - currentLowerBB) / (currentUpperBB - currentLowerBB);
            
            if (TRACE > 2) {
                asyncPrint(String.format("BB Position: %.3f (0=Lower, 1=Upper), RSI: %.2f", bbPosition, currentRSI));
            }
            
            // 1) 볼린져 밴드 상단 돌파 + RSI 과매수 구간: DOWN 신호만 허용
            if (currentPrice > currentUpperBB && currentRSI > 70) {
                if (TRACE > 1) asyncPrint("Strong DOWN condition: Price > BB Upper + RSI > 70");
                return basicSellSignal ? TradeSignal.DOWN : TradeSignal.HOLD;
            }
            
            // 2) 볼린져 밴드 하단 돌파 + RSI 과매도 구간: UP 신호만 허용
            if (currentPrice < currentLowerBB && currentRSI < 30) {
                if (TRACE > 1) asyncPrint("Strong UP condition: Price < BB Lower + RSI < 30");
                return basicBuySignal ? TradeSignal.UP : TradeSignal.HOLD;
            }
            
            // 3) 볼린져 밴드 상단 근처 (상위 20% 구간): DOWN 우선
            if (bbPosition > 0.8) {
                if (TRACE > 1) asyncPrint("Near BB upper band - DOWN preferred");
                return basicSellSignal ? TradeSignal.DOWN : TradeSignal.HOLD;
            }
            
            // 4) 볼린져 밴드 하단 근처 (하위 20% 구간): UP 우선
            if (bbPosition < 0.2) {
                if (TRACE > 1) asyncPrint("Near BB lower band - UP preferred");
                return basicBuySignal ? TradeSignal.UP : TradeSignal.HOLD;
            }
            
            // 5) RSI 기반 필터링 (중간 구간에서)
            if (currentRSI > 65) {
                if (TRACE > 1) asyncPrint("RSI > 65 - DOWN preferred");
                return basicSellSignal ? TradeSignal.DOWN : TradeSignal.HOLD;
            }
            
            if (currentRSI < 35) {
                if (TRACE > 1) asyncPrint("RSI < 35 - UP preferred");
                return basicBuySignal ? TradeSignal.UP : TradeSignal.HOLD;
            }
            
            // 6) 위 조건들에 해당하지 않으면 기본 신호 사용 (중립 구간)
            return basicBuySignal ? TradeSignal.UP : (basicSellSignal ? TradeSignal.DOWN : TradeSignal.HOLD);
        }

        /**
         * expertoption.java 스타일의 거래 실행
         */
        private boolean executeTrade(TradeSignal signal) {
            if (INIT_WAIT <= 0) {
                if (TRACE > 0) asyncPrint("Trading is disabled.");
                return false;
            }
        
            // 이미 거래가 진행중이거나, 정지된 상태면 실행하지 않음
            if (tradeStatus != TradeStatus.WAITING) {
                if (TRACE > 1) asyncPrint("Current trade status (" + tradeStatus + ") is not WAITING, trade rejected.");
                return false;
            }
        
            // 사이클 내에서 이미 거래한 방향으로는 재진입 방지
            if ((signal == TradeSignal.UP && hasTradedUpInCycle.get()) || 
                (signal == TradeSignal.DOWN && hasTradedDownInCycle.get())) {
                if (TRACE > 0) asyncPrint("Already executed " + signal + " trade in this cycle. Preventing duplicate trades.");
                return false;
            }
        
            tradeStatus = TradeStatus.TRADING; // 상태를 '거래 중'으로 변경
        
            if (signal == TradeSignal.UP) {
                hasTradedUpInCycle.set(true);
                Trade.INSTANCE.TradeStart(BUY_UP, "");
                asyncPrint(String.format("### UP Order Executed ### | Price(Spot/Futures): %.2f/%.2f | RSI: %.2f", 
                    tradePrice, tradePriceF, currentRSI));
            } else if (signal == TradeSignal.DOWN) {
                hasTradedDownInCycle.set(true);
                Trade.INSTANCE.TradeStart(SELL_DOWN, "");
                asyncPrint(String.format("### DOWN Order Executed ### | Price(Spot/Futures): %.2f/%.2f | RSI: %.2f", 
                    tradePrice, tradePriceF, currentRSI));
            }
        
            // 30초 후 거래 상태를 복구하고 사이클 완료를 체크하는 스레드
            new Thread(() -> {
                safeSleep(30000); // 30초 대기
        
                // UP/DOWN 거래가 모두 한 번씩 완료되었는지 확인
                if (hasTradedUpInCycle.get() && hasTradedDownInCycle.get()) {
                    tradeStatus = TradeStatus.STOPPED_BY_CYCLE;
                    asyncPrint("UP/DOWN trading cycle completed. Trading stopped until next initialization.");
                } else {
                    tradeStatus = TradeStatus.WAITING; // 사이클 미완료 시, 다음 거래를 위해 '대기' 상태로 전환
                    if (TRACE > 0) asyncPrint("Trade completed. Switching to waiting state for next signal.");
                }
            }).start();

            return true; // 거래 실행 성공
        }

        private void safeSleep(long millis) {
            try {
                Thread.sleep(millis);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // 과거 가격 변동의 절대값 평균을 계산하는 헬퍼 메서드도 deprecated
    } // End of MessageHandler class
    
 
    public static void InitMain() {
        // 사이클이 완료되었으면 플래그 초기화
        if (tradeStatus == TradeStatus.STOPPED_BY_CYCLE) {
            hasTradedUpInCycle.set(false);
            hasTradedDownInCycle.set(false);
            tradeStatus = TradeStatus.WAITING; // 다음 거래를 위해 WAITING 상태로 전환
            asyncPrint("Starting new trading cycle. Resetting UP/DOWN flags and changing trade status to WAITING.");
        }

        try {
            if (REFRESH_CNT > 20) {
                Trade.INSTANCE.TradeStart(REFRESH,"");
                REFRESH_CNT = 0;
                Trade.INSTANCE.TradeStart(TIME_RESET,"");
                Thread.sleep(10000);
            }
            REFRESH_CNT++;
            
            // 1. 데이터 가져오기 (현물/선물 모두 1분봉)
            List<Candlestick> candlestickListFutures = clientF.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 100, null, null);
            List<Candlestick> candlestickListSpot = client.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 100, null, null);

            // 2. 거래량 계산 (5개 캔들 평균)
            volume = calculateAverageVolume(candlestickListFutures, 5);
            volumeBTC = calculateAverageVolume(candlestickListSpot, 5);

            // 가격 데이터 리스트 생성
            List<Double> priceListSpot = new ArrayList<>();
            candlestickListSpot.forEach(c -> priceListSpot.add(Double.parseDouble(c.getClose())));
            List<Double> priceListFutures = new ArrayList<>();
            candlestickListFutures.forEach(c -> priceListFutures.add(Double.parseDouble(c.getClose())));

            // 3. 지표 계산 (동기화 블록으로 동시성 문제 방지)
            synchronized(BCGAME_Binary.class) {
                // 볼린져 밴드 계산 (20 기간, 2.0 표준편차)
                if (priceListFutures.size() >= 20) {
                    BollingerBands bb = new BollingerBands(priceListFutures, 20, 2.0);
                    currentUpperBB = bb.getUpBollingerBands();
                    currentLowerBB = bb.getdownBollingerBands();
                    currentMiddleBB = bb.getMidBollingerBands();
                    
                    // 기존 볼린져 밴드 변수도 업데이트
                    BollUp = currentUpperBB;
                    BollDown = currentLowerBB;
                }

                // RSI 계산 (14 기간 - 상태 보존)
                if (priceListFutures.size() >= 15) {
                    currentRSI = rsi14Calculator.calculate(priceListFutures);
                }
            }

            // 4. 설정 파일 로드
            loadProperties();

            // 5. 로그 출력 (개선된 거래 시스템)
            if (TRACE > 0) {
                asyncPrint("--- Enhanced Trading System Status ---");
                asyncPrint(String.format("Volume Limits (Spot/Futures): %.1f / %.1f", volumeBTC_Check, trade_volume_max));
                asyncPrint(String.format("Current Avg Volume (Spot/Futures): %.2f / %.2f", volumeBTC, volume));
                asyncPrint(String.format("Volume Condition: %s", 
                    ((volume < trade_volume_max) || (volumeBTC < volumeBTC_Check)) ? "MET" : "NOT MET"));
                
                if (!Double.isNaN(currentUpperBB) && !Double.isNaN(currentLowerBB)) {
                    asyncPrint(String.format("Bollinger Bands - Upper: %.2f, Middle: %.2f, Lower: %.2f", 
                        currentUpperBB, currentMiddleBB, currentLowerBB));
                }
                if (currentRSI != 50.0) {
                    String rsiCondition = currentRSI > 70 ? "Overbought" : (currentRSI < 30 ? "Oversold" : "Neutral");
                    asyncPrint(String.format("RSI (14): %.2f (%s)", currentRSI, rsiCondition));
                }
                asyncPrint(String.format("Trading Status: %s | Enabled: %s | Trace Level: %d", 
                    tradeStatus, (INIT_WAIT > 0 ? "YES" : "NO"), TRACE));
                asyncPrint("--------------------------------------");
            }
        } catch (Exception e) {
            asyncPrint("Error occurred during InitMain execution: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static double calculateAverageVolume(List<Candlestick> candlesticks, int count) {
        if (candlesticks == null || candlesticks.size() < count + 1) return 0;
        
        double totalVolume = 0;
        int endIndex = candlesticks.size() - 1; // 마지막 캔들(진행중) 제외
        int startIndex = Math.max(0, endIndex - count);
        
        // 정확히 count 개의 완료된 캔들만 계산
        for (int i = startIndex; i < endIndex; i++) {
            totalVolume += Double.parseDouble(candlesticks.get(i).getVolume());
        }
        
        int actualCount = endIndex - startIndex;
        return actualCount > 0 ? roundToTwoDecimalPlaces(totalVolume / actualCount) : 0;
    }
    
    private static void loadProperties() {
        String filePath = "c:/Trade/bcgame.properties";
        try (InputStream input = new FileInputStream(filePath)) {
            Properties prop = new Properties();
            prop.load(input);

            volumeBTC_Check = Double.parseDouble(prop.getProperty("volumeBTC_Check", "15"));
            trade_volume_max = Integer.parseInt(prop.getProperty("trade_volume_max", "15"));
            INIT_WAIT = Integer.parseInt(prop.getProperty("INIT_WAIT", "0"));
            TRACE = Integer.parseInt(prop.getProperty("TRACE", "0")); // 로그 레벨
            MONEY = prop.getProperty("MONEY", "2"); // 로그 레벨
            
        } catch (IOException | NumberFormatException e) {
            asyncPrint("Error loading or parsing configuration file: " + e.getMessage());
            // 기본값 유지 또는 오류 처리
        }
    }

    public static void main(String[] args) throws InterruptedException, IOException {
        // 바이낸스 클라이언트 생성 (API 키 필요 시 입력)
        BinancefactoryF = BinanceAbstractFactory.createFuturesFactory("", "");
        clientF = BinancefactoryF.newRestClient();

        factory = BinanceAbstractFactory.createSpotFactory("", "");
        client = factory.newRestClient();

        // 트레이딩 UI 실행 (추정)
        Trade.INSTANCE.TradeBitqDlg_expert();
        Trade.INSTANCE.TradeStart(WINDOWS_TOP,""); // 창 상단 고정?
        Trade.INSTANCE.TradeStart(TIME_RESET,""); // 시간 리셋?

        asyncPrint("Waiting for initialization (10 seconds)...");
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            e.printStackTrace();
        }
        asyncPrint("Initialization wait complete.");

        // 메시지 핸들러 인스턴스 생성 및 스레드 시작
        MessageHandler messageHandlerInstance = new MessageHandler(); // Create instance first
        Thread messageHandlerThread = new Thread(messageHandlerInstance); // Pass instance to thread constructor
        messageHandlerThread.setName("MessageHandlerThread");
        messageHandlerThread.start();
        asyncPrint("MessageHandler thread started.");

        asyncPrint("Main thread waiting started.");
        // 메인 스레드는 무한 대기 또는 다른 작업 수행
        while (true) {
            try {
                Thread.sleep(60000); // 1분마다 상태 출력 또는 확인
                // asyncPrint("메인 스레드 대기 중...");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                asyncPrint("Main thread interrupted. Shutting down...");
                // 필요시 하위 스레드 종료 로직 추가
                messageProcessingService.shutdownNow(); // 즉시 종료 시도
                PrintService.shutdownNow();

                // Wait for termination with timeout
                awaitTermination(messageProcessingService, "messageProcessingService");
                awaitTermination(PrintService, "PrintService");

                messageHandlerThread.interrupt(); // Interrupt the main handler thread
                // messageHandler1Thread.interrupt(); // Uncomment if MessageHandler1 is used
                break;
            }
        }
        asyncPrint("Main thread terminated.");
    }

    // Helper method for executor termination
    private static void awaitTermination(ExecutorService executor, String name) {
        try {
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                System.err.println(name + " did not terminate gracefully after shutdown(). Forcing shutdownNow().");
                List<Runnable> droppedTasks = executor.shutdownNow();
                System.err.println(name + " dropped " + droppedTasks.size() + " tasks.");
                // Wait a little longer for tasks to respond to cancellation
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    System.err.println(name + " did not terminate even after shutdownNow().");
                } else {
                    System.out.println(name + " terminated after shutdownNow().");
                }
            } else {
                System.out.println(name + " terminated gracefully.");
            }
        } catch (InterruptedException e) {
            System.err.println("Interrupted while waiting for " + name + " termination. Forcing shutdownNow().");
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    private static double roundToTwoDecimalPlaces(double value) {
        // 소수점 4자리까지 반올림 (가격 등에 사용될 수 있으므로 정밀도 유지)
        DecimalFormat df_local = new DecimalFormat("#.####");
        // DecimalFormat은 스레드 안전하지 않으므로 지역 변수로 사용하거나 동기화 필요
        // 여기서는 지역 변수로 사용
        try {
            return Double.parseDouble(df_local.format(value));
        } catch (NumberFormatException e) {
            return value; // 오류 시 원본 값 반환
        }
    }
}
