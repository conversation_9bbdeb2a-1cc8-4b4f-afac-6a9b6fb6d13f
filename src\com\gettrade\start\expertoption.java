package com.gettrade.start;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

import com.binance.api.client.api.sync.BinanceApiFuturesRestClient;
import com.binance.api.client.api.sync.BinanceApiSpotRestClient;
import com.binance.api.client.domain.market.Candlestick;
import com.binance.api.client.domain.market.CandlestickInterval;
import com.binance.api.client.factory.BinanceAbstractFactory;
import com.binance.api.client.factory.BinanceFuturesApiClientFactory;
import com.binance.api.client.factory.BinanceSpotApiClientFactory;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.stream.JsonReader;
import com.sun.jna.Library;
import com.sun.jna.Native;

public class expertoption {

    public enum TradeSignal {
        UP, DOWN, HOLD
    }

    public enum TradeStatus {
        WAITING, // 거래 대기
        TRADING, // 거래 진행 중
        STOPPED_BY_VOLUME, // 거래량으로 인한 거래 중지
        STOPPED_BY_CYCLE,  // UP/DOWN 1회 완료 후 중지
        STOPPED_MANUALLY // 수동 중지
    }

    // JNA 인터페이스: 외부 DLL 라이브러리 연동
    public interface Trade extends Library {
        Trade INSTANCE = Native.loadLibrary("MTrade", Trade.class);

        void TradeBitqDlg_expert();
        int GetUpDownSecRcv();
        void TradeStart(int nflag);
    }

    // 거래 상수
    public static final int BUY_UP = 500002;
    public static final int SELL_DOWN = 500001;
    public static final int CHECK = 500003;
    public static final int CHECK_PAYOUT = 500004;
    public static final int CHECK_RESULT = 500005; // 거래 결과 확인 (가상)
    public static final int WINDOWS_TOP = 300008;
    public static final int TIME_RESET = 4;
    public static final int REFRESH = 304;
    public static final int TRADE_STOP = 103;
    public static int REFRESH_CNT = 0;
    // 설정 및 상태 변수
    public static volatile double volume = 0;
    public static volatile double volumeBTC = 0;
    public static volatile double volumeBTC_Check = 10; // Aldar와 동일한 기본값으로 변경
    public static volatile double trade_volume_max = 100; // Aldar와 동일한 기본값으로 변경
    public static volatile TradeStatus tradeStatus = TradeStatus.WAITING;
    public static volatile int isTradingEnabled = 0;
    public static volatile int TRACE = 0;

    public static volatile double atrValue = 0.005; // 기본값 초기화

    // RSI 계산기를 정적 변수로 유지하여 상태 보존
    private static RSI rsi14Calculator = new RSI(14);
    private static ADX adx14Calculator = new ADX(14);
    
    // 지표 공유 변수
    public static volatile double standardDeviation = 0.0; // 표준편차
    public static volatile double currentADX = 25.0; // ADX 값 (기본값: 횡보)
    public static volatile double currentPlusDI = 50.0; // +DI 값
    public static volatile double currentMinusDI = 50.0; // -DI 값
    public static volatile double priceRangePercent = 0.0; // 가격 범위 백분율
    
    // 볼린져 밴드 및 RSI 변수
    public static volatile double currentRSI = 50.0;
    public static volatile double currentUpperBB = Double.NaN;
    public static volatile double currentLowerBB = Double.NaN;
    public static volatile double currentMiddleBB = Double.NaN;

    // 바이낸스 클라이언트
    public static BinanceFuturesApiClientFactory BinancefactoryF = null;
    public static BinanceApiFuturesRestClient clientF = null;
    public static BinanceSpotApiClientFactory factory = null;
    public static BinanceApiSpotRestClient client = null;

    private static AtomicBoolean initTrade = new AtomicBoolean(false);
    private static AtomicBoolean first = new AtomicBoolean(false);
    private static AtomicBoolean hasTradedUpInCycle = new AtomicBoolean(false);
    private static AtomicBoolean hasTradedDownInCycle = new AtomicBoolean(false);
    public static String symbol = "BTCUSDT";

    private static final ExecutorService messageProcessingService = Executors.newCachedThreadPool();
    private static final ExecutorService PrintService = Executors.newSingleThreadExecutor();

    public static void asyncPrint(String message) {
        PrintService.submit(() -> System.out.println(message));
    }

    // --- 지표 계산 클래스들 (RSI, EMA, ROC) ---
    public static class RSI {
        private final int period;

        public RSI(int period) {
            this.period = period;
        }

        public double calculate(List<Double> prices) {
            // 상태를 가지지 않고 매번 전체 데이터를 기반으로 재계산하여 정확성을 보장합니다.
            if (prices.size() < period + 1) {
                return 50.0; // 데이터 부족
            }

            List<Double> gains = new ArrayList<>();
            List<Double> losses = new ArrayList<>();

            // 가격 변화에 따른 이득과 손실 리스트 생성
            for (int i = 1; i < prices.size(); i++) {
                double change = prices.get(i) - prices.get(i - 1);
                if (change > 0) {
                    gains.add(change);
                    losses.add(0.0);
                } else {
                    gains.add(0.0);
                    losses.add(Math.abs(change));
                }
            }

            // 초기 평균 계산 (SMA)
            double avgGain = 0;
            double avgLoss = 0;
            for (int i = 0; i < period; i++) {
                avgGain += gains.get(i);
                avgLoss += losses.get(i);
            }
            avgGain /= period;
            avgLoss /= period;

            // Wilder's Smoothing 적용
            for (int i = period; i < gains.size(); i++) {
                avgGain = (avgGain * (period - 1) + gains.get(i)) / period;
                avgLoss = (avgLoss * (period - 1) + losses.get(i)) / period;
            }

            if (avgLoss == 0) {
                return 100.0;
            }

            double rs = avgGain / avgLoss;
            return 100.0 - (100.0 / (1.0 + rs));
        }
    }
    
    // ADX (Average Directional Index) 클래스 - 추세 방향 및 강도 감지용
    public static class ADX {
        private final int period;
        private double currentPlusDI = 0.0;
        private double currentMinusDI = 0.0;
        private double currentADXValue = 25.0;
        
        public ADX(int period) {
            this.period = period;
        }

        /**
         * Wilder's Smoothing (EMA with alpha = 1/period)을 적용하는 헬퍼 메소드
         */
        private double[] getSmoothedValues(List<Double> values, int period) {
            double[] smoothed = new double[values.size()];
            if (values.isEmpty() || values.size() < period) return smoothed;

            // 첫 값은 SMA(단순 이동 평균)로 초기화
            double sum = 0;
            for (int i = 0; i < period; i++) {
                sum += values.get(i);
            }
            smoothed[period - 1] = sum / period;

            // 이후 값들은 Wilder's Smoothing 적용 (표준 EMA 공식)
            for (int i = period; i < values.size(); i++) {
                smoothed[i] = (smoothed[i - 1] * (period - 1) + values.get(i)) / period;
            }
            return smoothed;
        }
        
        public double calculate(List<Candlestick> candlesticks) {
            // 안정적인 ADX 계산을 위해 최소 2 * period + 1 개의 캔들 필요
            if (candlesticks.size() < (period * 2) + 1) {
                currentADXValue = 25.0; // 데이터 부족 시 중립 값 반환
                currentPlusDI = 50.0;
                currentMinusDI = 50.0;
                return currentADXValue;
            }
            
            List<Double> highList = new ArrayList<>();
            List<Double> lowList = new ArrayList<>();
            List<Double> closeList = new ArrayList<>();
            for(Candlestick c : candlesticks) {
                highList.add(Double.parseDouble(c.getHigh()));
                lowList.add(Double.parseDouble(c.getLow()));
                closeList.add(Double.parseDouble(c.getClose()));
            }

            List<Double> trList = new ArrayList<>();
            List<Double> plusDMList = new ArrayList<>();
            List<Double> minusDMList = new ArrayList<>();

            for (int i = 1; i < candlesticks.size(); i++) {
                double high = highList.get(i);
                double low = lowList.get(i);
                double prevHigh = highList.get(i-1);
                double prevLow = lowList.get(i-1);
                double prevClose = closeList.get(i-1);
                
                // True Range (TR)
                double tr = Math.max(high - low, Math.max(Math.abs(high - prevClose), Math.abs(low - prevClose)));
                trList.add(tr);

                // Directional Movement (+DM, -DM)
                double upMove = high - prevHigh;
                double downMove = prevLow - low;
                plusDMList.add((upMove > downMove && upMove > 0) ? upMove : 0.0);
                minusDMList.add((downMove > upMove && downMove > 0) ? downMove : 0.0);
            }
            
            // TR, +DM, -DM 스무딩 (ATR, Smoothed +DM, Smoothed -DM)
            double[] smoothedTR = getSmoothedValues(trList, period);
            double[] smoothedPlusDM = getSmoothedValues(plusDMList, period);
            double[] smoothedMinusDM = getSmoothedValues(minusDMList, period);

            List<Double> dxList = new ArrayList<>();
            for (int i = period -1; i < smoothedTR.length; i++) {
                if (smoothedTR[i] > 0) {
                    double plusDI = 100 * (smoothedPlusDM[i] / smoothedTR[i]);
                    double minusDI = 100 * (smoothedMinusDM[i] / smoothedTR[i]);
                    double sumDI = plusDI + minusDI;
                    if (sumDI != 0) {
                        dxList.add(100 * Math.abs(plusDI - minusDI) / sumDI);
                        // 마지막 값들을 저장 (추세 방향 판단용)
                        if (i == smoothedTR.length - 1) {
                            currentPlusDI = plusDI;
                            currentMinusDI = minusDI;
                        }
                    } else {
                        dxList.add(0.0);
                    }
                } else {
                    dxList.add(0.0);
                }
            }
            
            if (dxList.size() < period) {
                currentADXValue = 25.0; // 데이터 부족
                currentPlusDI = 50.0;
                currentMinusDI = 50.0;
                return currentADXValue;
            }

            // DX를 스무딩하여 ADX 계산
            double[] adxValues = getSmoothedValues(dxList, period);
            
            // 마지막 계산된 ADX 값을 반환 (인덱스 주의)
            currentADXValue = adxValues[adxValues.length-1];
            return currentADXValue;
        }
        
        public double getPlusDI() {
            return currentPlusDI;
        }
        
        public double getMinusDI() {
            return currentMinusDI;
        }
        
        public boolean isUpTrend() {
            return currentPlusDI > currentMinusDI;
        }
        
        public boolean isDownTrend() {
            return currentMinusDI > currentPlusDI;
        }
    }
    
    // Aldar 스타일 볼린져 밴드 클래스 추가
    public static class BollingerBands {
        private double upperBand;
        private double lowerBand;
        private double middleBand;

        public BollingerBands(List<Double> prices, int period, double stdDevFactor) {
            if (prices.size() >= period) {
                List<Double> subset = prices.subList(prices.size() - period, prices.size());
                calculateBollingerBands(subset, stdDevFactor);
            }
        }

        private void calculateBollingerBands(List<Double> prices, double stdDevFactor) {
            double sma = calculateSMA(prices);
            double stdDev = calculateStdDev(prices, sma);

            this.upperBand = sma + stdDevFactor * stdDev;
            this.lowerBand = sma - stdDevFactor * stdDev;
            this.middleBand = sma;
        }

        private double calculateSMA(List<Double> prices) {
            double sum = 0.0;
            for (double price : prices) {
                sum += price;
            }
            return sum / prices.size();
        }

        private double calculateStdDev(List<Double> prices, double sma) {
            double sum = 0.0;
            for (double price : prices) {
                sum += Math.pow(price - sma, 2.0);
            }
            // 볼린져 밴드에서는 모집단 표준편차 사용 (Population Standard Deviation)
            // 분모는 N (prices.size())을 사용 (샘플 표준편차의 경우 N-1을 사용)
            double variance = sum / prices.size();
            return Math.sqrt(variance);
        }
        
        public double getUpBollingerBands() {
            return upperBand;
        }
        
        public double getdownBollingerBands() {
            return lowerBand;
        }
        
        public double getMidBollingerBands() {
            return middleBand;
        }
    }
    
    public static double calculateROC(double currentPrice, double previousPrice) {
        if (previousPrice == 0) return 0;
        return ((currentPrice - previousPrice) / previousPrice) * 100;
    }

    // Wilder's Smoothing을 사용한 ATR 계산 헬퍼 메소드
    private static double calculateATR(List<Candlestick> candlesticks, int period) {
        if (candlesticks == null || candlesticks.size() < period + 1) {
            return 0.0; // ATR 계산에 필요한 최소 데이터가 부족한 경우
        }

        List<Double> trueRanges = new ArrayList<>();
        for (int i = 1; i < candlesticks.size(); i++) {
            Candlestick current = candlesticks.get(i);
            Candlestick previous = candlesticks.get(i - 1);

            double high = Double.parseDouble(current.getHigh());
            double low = Double.parseDouble(current.getLow());
            double prevClose = Double.parseDouble(previous.getClose());

            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);
            double tr = Math.max(tr1, Math.max(tr2, tr3));
            trueRanges.add(tr);
        }

        if (trueRanges.size() < period) {
            return 0.0;
        }

        // 초기 ATR은 처음 'period'개의 TR의 SMA로 계산
        double atr = 0.0;
        for (int i = 0; i < period; i++) {
            atr += trueRanges.get(i);
        }
        atr /= period;

        // 이후 ATR은 Wilder's Smoothing 적용
        for (int i = period; i < trueRanges.size(); i++) {
            atr = (atr * (period - 1) + trueRanges.get(i)) / period;
        }

        return atr;
    }

    // --- 주 거래 로직 ---
    static class MessageHandler implements Runnable {
        private volatile double tradeOrderbookDownF20 = Double.NaN;
        private volatile double tradeOrderbookUpF20 = Double.NaN;
        private volatile double tradeOrderbookDown20 = Double.NaN;
        private volatile double tradeOrderbookUp20 = Double.NaN;
        private volatile double currentFuturesPrice = -999999;
        private volatile double currentSpotPrice = -999999;
        private volatile double oldtradePriceF = -999999;
        private volatile double oldtradePrice = -999999;
        private volatile boolean bfirstBTC20 = false;
        private volatile boolean bfirstFBTC20 = false;
        private long lastInitMillis = -1; // 5초 주기 실행을 제어하기 위한 밀리초 기록

        @Override
        public void run() {
            while (true) {
                try (Socket socket = new Socket("127.0.0.1", 22222)) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                    asyncPrint("Socket connection successful. Starting data reception.");
                    resetDataFlags();

                    while (true) {
                        String message = in.readLine();
                        if (message != null && !message.isEmpty()) {
                            long totalMilliseconds = System.currentTimeMillis();
                            long currentSecond = (totalMilliseconds / 1000) % 60;

                            // 매 분 59초 또는 첫 실행 시 전체 초기화
                            if ((currentSecond == 59 || !first.get())) {
                                if (initTrade.compareAndSet(false, true)) {
                                    REFRESH_CNT++;
                                    messageProcessingService.submit(() -> {
                                        try {
                                            Trade.INSTANCE.TradeStart(CHECK); // 상태 체크
                                            if (REFRESH_CNT > 20) {
                                                Trade.INSTANCE.TradeStart(REFRESH);
                                                REFRESH_CNT = 0;
                                                Trade.INSTANCE.TradeStart(TIME_RESET);
                                                Thread.sleep(5000);
                                            }

                                            InitMain(false); // 설정 및 데이터 업데이트
                                            resetTradeOrderbook();
                                            resetDataFlags();
                                             if (Trade.INSTANCE.GetUpDownSecRcv() != 1) {
                                                isTradingEnabled = 0; // 설정 파일 반영
                                                asyncPrint("isTradingEnabled CHECK setting applied: " + isTradingEnabled);
                                            }
                                            
                                            Trade.INSTANCE.TradeStart(CHECK_PAYOUT);
                                            Thread.sleep(1000);
                                            if (Trade.INSTANCE.GetUpDownSecRcv() != 10) {
                                                isTradingEnabled = 0; // 설정 파일 반영
                                                asyncPrint("isTradingEnabled CHECK_PAYOUT setting applied: " + isTradingEnabled);
                                            }

                                            // 사이클 상태 관리
                                            if (tradeStatus != TradeStatus.STOPPED_BY_CYCLE && tradeStatus != TradeStatus.STOPPED_MANUALLY) {
                                                tradeStatus = TradeStatus.WAITING; 
                                            }
                                            
                                            asyncPrint("Full initialization completed. Trade status: " + tradeStatus);
                                            first.set(true);
                                            lastInitMillis = totalMilliseconds; // 마지막 실행 시간 기록
                                        } catch (Exception e) {
                                            asyncPrint("Error during full initialization: " + e.getMessage());
                                        } finally {
                                            initTrade.set(false);
                                        }
                                    });
                                }
                            }
                            // 매 5초마다 데이터 업데이트 (59초 제외) - 5초 간격으로 한 번만 실행
                            else if (currentSecond % 5 == 0 && currentSecond != 59 && 
                                    (lastInitMillis == -1 || (totalMilliseconds - lastInitMillis) >= 5000)) {
                                if (initTrade.compareAndSet(false, true)) {
                                    lastInitMillis = totalMilliseconds; // 실행 시간을 밀리초로 기록
                                    messageProcessingService.submit(() -> {
                                        try {
                                            InitMain(true); 
                                            if (TRACE > 0) asyncPrint("Periodic 5-second data update completed.");
                                        } catch (Exception e) {
                                            asyncPrint("Error during periodic 5s reset: " + e.getMessage());
                                        } finally {
                                            initTrade.set(false);
                                        }
                                    });
                                }
                            }
                            // 그 외 시간에는 메시지 처리
                            else if (first.get()) {
                                if (initTrade.compareAndSet(false, true)) { // 다른 작업 중이 아닐 때만 메시지 처리
                                    try {
                                        processMessage(message); // 핵심 로직: 메시지 처리 및 거래 결정
                                    } catch (Exception e) {
                                        asyncPrint("Error while processing message: " + e.getMessage());
                                        e.printStackTrace();
                                    } finally {
                                        initTrade.set(false);
                                    }
                                }
                            }
                        }
                    }
                } catch (IOException e) {
                    handleConnectionError(e);
                } catch (Exception ex) {
                    asyncPrint("Unexpected error in MessageHandler loop: " + ex.getMessage());
                    safeSleep(1000);
                }
            }
        }

        private void processMessage(String message) {
            if (message == null || message.isEmpty()) return;
            // JSON 파싱
            try (JsonReader reader = new JsonReader(new StringReader(message))) {
                reader.setLenient(true);
                while (reader.hasNext()) {
                    JsonObject jsonObject = JsonParser.parseReader(reader).getAsJsonObject();
                    if (jsonObject.has("Mtype") && jsonObject.has("message")) {
                        String mtype = jsonObject.get("Mtype").getAsString();
                        String msg = jsonObject.get("message").getAsString();
                        parseOrderbookData(mtype, msg);
                        // 모든 데이터가 수신되었고 거래 가능한 상태일 때만 로직 실행
                        if (bfirstBTC20 && bfirstFBTC20 && tradeStatus == TradeStatus.WAITING) {
                            TradeSignal signal = determineTradeAction();
                            if (signal != TradeSignal.HOLD) {
                                executeTrade(signal);
                            }
                        }
                    }
                    oldtradePriceF = currentFuturesPrice;
                    oldtradePrice = currentSpotPrice;
                }
            } catch (Exception e) {
                 if (TRACE > 0) asyncPrint("JSON parsing error: " + e.getMessage());
            }
        }
        
        private void parseOrderbookData(String mtype, String msg) {
            try {
                String[] data = msg.split("-", 3);
                if (data.length >= 3) {
                    if ("BTC20".equals(mtype)) {
                        tradeOrderbookDown20 = Double.parseDouble(data[0]);
                        tradeOrderbookUp20 = Double.parseDouble(data[1]);
                        currentSpotPrice = Double.parseDouble(data[2]);
                        bfirstBTC20 = true;
                    } else if ("FBTC20".equals(mtype)) {
                        tradeOrderbookDownF20 = Double.parseDouble(data[0]);
                        tradeOrderbookUpF20 = Double.parseDouble(data[1]);
                        currentFuturesPrice = Double.parseDouble(data[2]);
                        bfirstFBTC20 = true;
                    }
                }
            } catch (NumberFormatException e) {
                if (TRACE > 0) asyncPrint("Number format error: " + msg);
            }
        }

        private TradeSignal determineTradeAction() {
            // 1. 기본 오더북 거래 신호 체크
            boolean basicBuySignal = tradeOrderbookUp20 < 1 || tradeOrderbookUpF20 < 1;
            boolean basicSellSignal = tradeOrderbookDown20 < 1 || tradeOrderbookDownF20 < 1;

            if(atrValue >= 0.00025 && atrValue < 0.0005 && volume < 100 && volumeBTC < 10) {
                basicBuySignal = tradeOrderbookUp20 < 1 && tradeOrderbookUpF20 < 1;
                basicSellSignal = tradeOrderbookDown20 < 1 && tradeOrderbookDownF20 < 1;
            }else if(atrValue > 0.0005 ) {
                asyncPrint("Warning: ATR values indicate high volatility for 30s binary trading.");
                return TradeSignal.HOLD;
            }

            // 2. 기본 신호가 없으면 HOLD
            if ((!basicBuySignal && !basicSellSignal) || (basicBuySignal && basicSellSignal)) {
                return TradeSignal.HOLD;
            }
              
            // 3. 거래량 제한 체크 (AND 조건: 선물과 현물 모두 거래량이 적어야 거래 가능)
            boolean volumeConditionMet = (volume < trade_volume_max) || (volumeBTC < volumeBTC_Check);
            if (!volumeConditionMet) {
                if (TRACE > 0) {
                    asyncPrint(String.format("Volume condition not met - Futures: %.2f/%.2f, Spot: %.2f/%.2f", 
                        volume, trade_volume_max, volumeBTC, volumeBTC_Check));
                }
                return TradeSignal.HOLD;
            }
            
            // 4. 기술적 분석 필터 적용
            TradeSignal filteredSignal = applyTechnicalFilters(basicBuySignal, basicSellSignal);
            
            if (filteredSignal != TradeSignal.HOLD && TRACE > 0) {
                // 로컬 변수로 다시 한 번 동기화된 값들을 가져와 로그에 출력
                double logRSI, logUpperBB, logLowerBB, logMiddleBB;
                synchronized(expertoption.class) {
                    logRSI = currentRSI;
                    logUpperBB = currentUpperBB;
                    logLowerBB = currentLowerBB;
                    logMiddleBB = currentMiddleBB;
                }
                asyncPrint(String.format("### %s Signal ### RSI:%.2f, BB(U/M/L):%.2f/%.2f/%.2f, Price(Futures):%.2f", 
                    filteredSignal, logRSI, logUpperBB, logMiddleBB, logLowerBB, currentFuturesPrice));
            }
            
            return filteredSignal;
        }
        
        private TradeSignal applyTechnicalFilters(boolean basicBuySignal, boolean basicSellSignal) {
            double currentPrice = currentFuturesPrice; // 현물 가격(currentSpotPrice) 대신 지표 계산에 사용된 선물 가격(currentFuturesPrice)을 사용하도록 수정
            
            // 지표 값들을 로컬 변수로 복사하여 일관성 보장
            double localRSI, localUpperBB, localLowerBB, localADX, localPlusDI, localMinusDI, localPriceRange;
            synchronized(expertoption.class) {
                localRSI = currentRSI;
                localUpperBB = currentUpperBB;
                localLowerBB = currentLowerBB;
                localADX = currentADX;
                localPlusDI = currentPlusDI;
                localMinusDI = currentMinusDI;
                localPriceRange = priceRangePercent;
            }
            
            // 볼린져 밴드와 RSI가 계산되지 않은 경우 거래 보류
            if (Double.isNaN(localUpperBB) || Double.isNaN(localLowerBB) || localRSI == 50.0) {
                if (TRACE > 0) asyncPrint("Technical indicators not ready, holding trade.");
                return TradeSignal.HOLD;
            }
  
            // 현재 가격의 볼린져 밴드 내 위치 계산 (제로 디비전 방지)
            double bandWidth = localUpperBB - localLowerBB;
            double bbPosition = 0.5; // 기본값: 중간 위치
            if (bandWidth > 0) {
                bbPosition = (currentPrice - localLowerBB) / bandWidth;
            }

            // *** ADX 기반 추세 분석 및 거래 신호 결정 ***
            if (localADX > 20.0) {
                return TradeSignal.HOLD;
                /*
                if (bbPosition > 0.85) {
                    return TradeSignal.HOLD;
                }

                if (bbPosition < 0.15) {
                    return TradeSignal.HOLD;
                }
                // *** 볼린져 밴드 내에서만 거래 허용 ***
                if (currentPrice >= localUpperBB || currentPrice <= localLowerBB) {
                    if (TRACE > 0) asyncPrint(String.format("Price outside Bollinger Bands (%.2f), holding trade. BB Range: %.2f - %.2f", 
                        currentPrice, localLowerBB, localUpperBB));
                    return TradeSignal.HOLD;
                }

                // 강한 추세 상황: 추세 방향에 따라 거래
                boolean isUpTrend = localPlusDI > localMinusDI;
                boolean isDownTrend = localMinusDI > localPlusDI;
                
                if (isUpTrend && basicBuySignal) {
                    if (TRACE > 0) asyncPrint(String.format("Strong uptrend detected (ADX: %.2f, +DI: %.2f > -DI: %.2f), UP signal allowed", 
                        localADX, localPlusDI, localMinusDI));
                    return TradeSignal.UP;
                } else if (isDownTrend && basicSellSignal) {
                    if (TRACE > 0) asyncPrint(String.format("Strong downtrend detected (ADX: %.2f, -DI: %.2f > +DI: %.2f), DOWN signal allowed", 
                        localADX, localMinusDI, localPlusDI));
                    return TradeSignal.DOWN;
                } else {
                    if (TRACE > 0) asyncPrint(String.format("Strong trend detected but signal doesn't match trend direction, holding trade"));
                    return TradeSignal.HOLD;
                }
            */
            } else {
                // 횡보 구간 (ADX <= 25): 기존 로직 적용
                
                // 가격 Range 기반 변동성 체크: 범위가 0.8% 이상이면 변동성이 높다고 판단
                if (localPriceRange > 0.008) {
                    if (TRACE > 0) asyncPrint(String.format("High price volatility detected (Range: %.3f%% > 0.8%%), holding trade", localPriceRange * 100));
                    return TradeSignal.HOLD;
                }
                
                if (TRACE > 0) {
                    asyncPrint(String.format("Sideways market conditions met - ADX: %.2f, Price Range: %.3f%%", localADX, localPriceRange * 100));
                }
                
                // 1) 볼린져 밴드 상단 돌파 + RSI 과매수 구간: DOWN 신호만 허용
                if (currentPrice > localUpperBB && localRSI > 75) {
                    if (TRACE > 0) asyncPrint("Strong DOWN condition: Price > BB Upper + RSI > 70");
                    return basicSellSignal ? TradeSignal.DOWN : TradeSignal.HOLD;
                }
                
                // 2) 볼린져 밴드 하단 돌파 + RSI 과매도 구간: UP 신호만 허용
                if (currentPrice < localLowerBB && localRSI < 25) {
                    if (TRACE > 0) asyncPrint("Strong UP condition: Price < BB Lower + RSI < 30");
                    return basicBuySignal ? TradeSignal.UP : TradeSignal.HOLD;
                }
                // 3) 볼린져 밴드 상단 근처 (상위 10% 구간): DOWN 우선
                if (bbPosition > 0.90) {
                    if (TRACE > 0) asyncPrint("Near BB upper band - DOWN preferred");
                    return basicSellSignal ? TradeSignal.DOWN : TradeSignal.HOLD;
                }
                
                // 4) 볼린져 밴드 하단 근처 (하위 10% 구간): UP 우선
                if (bbPosition < 0.10) {
                    if (TRACE > 0) asyncPrint("Near BB lower band - UP preferred");
                    return basicBuySignal ? TradeSignal.UP : TradeSignal.HOLD;
                }
                
                // 5) RSI 기반 필터링 (중간 구간에서)
                if (localRSI > 75) {
                    if (TRACE > 0) asyncPrint("RSI > 75 - DOWN preferred");
                    return basicSellSignal ? TradeSignal.DOWN : TradeSignal.HOLD;
                }
                
                if (localRSI < 25) {
                    if (TRACE > 0) asyncPrint("RSI < 25 - UP preferred");
                    return basicBuySignal ? TradeSignal.UP : TradeSignal.HOLD;
                }
                
                // 중립 구간 개선: 더 엄격한 조건 적용
                // RSI가 중간 범위(30-70)에 있을 때 추가 필터링
                if (localRSI >= 30 && localRSI <= 70) {
                    // 볼린져 밴드 중간 영역(20%-80%)에서는 신호 강도가 약하므로 보수적 접근
                    if (bbPosition >= 0.20 && bbPosition <= 0.80) {
                        // 기본 신호가 있더라도 추가 조건을 만족해야 거래 실행
                        
                        // UP 신호의 경우: RSI가 하향 추세이고 볼린져 밴드 하위 40% 영역
                        if (basicBuySignal && localRSI < 50 && bbPosition < 0.40) {
                            if (TRACE > 0) asyncPrint(String.format("Neutral zone UP signal - RSI: %.2f, BB Position: %.2f", localRSI, bbPosition));
                            return TradeSignal.UP;
                        }
                        
                        // DOWN 신호의 경우: RSI가 상향 추세이고 볼린져 밴드 상위 60% 영역
                        if (basicSellSignal && localRSI > 50 && bbPosition > 0.60) {
                            if (TRACE > 0) asyncPrint(String.format("Neutral zone DOWN signal - RSI: %.2f, BB Position: %.2f", localRSI, bbPosition));
                            return TradeSignal.DOWN;
                        }
                        
                        // 조건을 만족하지 않으면 거래 보류
                        if (TRACE > 0) asyncPrint(String.format("Neutral zone - conditions not met for trading. RSI: %.2f, BB Position: %.2f", localRSI, bbPosition));
                        return TradeSignal.HOLD;
                    }
                }
                
                // 4) 극단적인 RSI 값이 아닌 경우에만 기본 신호 사용
                // RSI가 35-65 범위에 있으면 신호 강도가 약하므로 거래 보류
                if (localRSI >= 35 && localRSI <= 65) {
                    if (TRACE > 0) asyncPrint(String.format("RSI in weak signal range (%.2f), holding trade", localRSI));
                    return TradeSignal.HOLD;
                }
                
                // 위 조건들을 통과한 경우에만 기본 신호 사용
                return basicBuySignal ? TradeSignal.UP : (basicSellSignal ? TradeSignal.DOWN : TradeSignal.HOLD);
            }
        }

        private boolean executeTrade(TradeSignal signal) {
            if (isTradingEnabled <= 0) {
                if (TRACE > 0) asyncPrint("Trading is disabled.");
                return false;
            }
        
            // 이미 거래가 진행중이거나, 중지된 상태면 실행하지 않음
            if (tradeStatus != TradeStatus.WAITING) {
                if (TRACE > 0) asyncPrint("Current trade status (" + tradeStatus + ") is not WAITING, trade rejected.");
                return false;
            }
        
            // 사이클 내에서 이미 거래한 방향으로는 재진입 방지
            if ((signal == TradeSignal.UP && hasTradedUpInCycle.get()) || 
                (signal == TradeSignal.DOWN && hasTradedDownInCycle.get())) {
                if (TRACE > 0) asyncPrint("Already executed " + signal + " trade in this cycle. Preventing duplicate trades.");
                return false;
            }
        
            tradeStatus = TradeStatus.TRADING; // 상태를 '거래 중'으로 변경
        
            if (signal == TradeSignal.UP) {
                hasTradedUpInCycle.set(true);
                Trade.INSTANCE.TradeStart(BUY_UP);
                asyncPrint(String.format("### UP Order Executed ### | Price(Spot/Futures): %.2f/%.2f | RSI: %.2f | ADX: %.2f (+DI:%.2f/-DI:%.2f) | PriceRange: %.3f%%", 
                    currentSpotPrice, currentFuturesPrice, currentRSI, currentADX, currentPlusDI, currentMinusDI, priceRangePercent * 100));
            } else if (signal == TradeSignal.DOWN) {
                hasTradedDownInCycle.set(true);
                Trade.INSTANCE.TradeStart(SELL_DOWN);
                asyncPrint(String.format("### DOWN Order Executed ### | Price(Spot/Futures): %.2f/%.2f | RSI: %.2f | ADX: %.2f (+DI:%.2f/-DI:%.2f) | PriceRange: %.3f%%", 
                    currentSpotPrice, currentFuturesPrice, currentRSI, currentADX, currentPlusDI, currentMinusDI, priceRangePercent * 100));
            }
        
            // 30초 후 거래 상태를 복구하고 사이클 완료를 체크하는 스레드
            new Thread(() -> {
                safeSleep(30000); // 30초 대기
        
                // UP/DOWN 거래가 모두 한 번씩 완료되었는지 확인
                if (hasTradedUpInCycle.get() && hasTradedDownInCycle.get()) {
                    tradeStatus = TradeStatus.STOPPED_BY_CYCLE;
                    asyncPrint("UP/DOWN trading cycle completed. Trading stopped until next initialization.");
                } else {
                    tradeStatus = TradeStatus.WAITING; // 사이클 미완료 시, 다음 거래를 위해 '대기' 상태로 전환
                    if (TRACE > 0) asyncPrint("Trade completed. Switching to waiting state for next signal.");
                }
            }).start();

            return true; // 거래 실행 성공
        }

        private void resetTradeOrderbook() {
            tradeOrderbookDownF20 = Double.NaN;
            tradeOrderbookUpF20 = Double.NaN;
            tradeOrderbookDown20 = Double.NaN;
            tradeOrderbookUp20 = Double.NaN;
            currentFuturesPrice = -999999;
            currentSpotPrice = -999999;
            oldtradePriceF = -999999;
            oldtradePrice = -999999;
        }

        private void resetDataFlags() {
            bfirstBTC20 = false;
            bfirstFBTC20 = false;
        }

        private void handleConnectionError(IOException e) {
            asyncPrint("Socket connection error: " + e.getMessage());
            resetTradeOrderbook();
            resetDataFlags();
            initTrade.set(false);
            first.set(false);
            safeSleep(5000); // 5초 후 재연결 시도
        }
        
        private void safeSleep(long millis) {
            try {
                Thread.sleep(millis);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    public static void InitMain(boolean bfiveInit) {
        // 사이클이 완료되었으면 플래그 초기화
        if (tradeStatus == TradeStatus.STOPPED_BY_CYCLE && !bfiveInit) {
            hasTradedUpInCycle.set(false);
            hasTradedDownInCycle.set(false);
            tradeStatus = TradeStatus.WAITING; // 다음 거래를 위해 WAITING 상태로 전환
            asyncPrint("Starting new trading cycle. Resetting UP/DOWN flags and changing trade status to WAITING.");
        }

        try {
            // 1. 데이터 가져오기 (현물/선물 모두 1분봉)
            List<Candlestick> candlestickListFutures = clientF.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 100, null, null);
            List<Candlestick> candlestickListSpot = client.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 100, null, null);

            // 2. 거래량 계산 (10개 캔들 평균)
            volume = calculateAverageVolume(candlestickListFutures, 5);
            volumeBTC = calculateAverageVolume(candlestickListSpot, 5);

            // 가격 데이터 리스트 생성
            List<Double> priceListFutures = new ArrayList<>();
            candlestickListFutures.forEach(c -> priceListFutures.add(Double.parseDouble(c.getClose())));

            // 3. 지표 계산 (동기화 블록으로 동시성 문제 방지)
            synchronized(expertoption.class) {
                // 볼린져 밴드 계산 (20 기간, 2.0 표준편차)
                if (priceListFutures.size() >= 20) {
                    BollingerBands bb = new BollingerBands(priceListFutures, 20, 2.0);
                    currentUpperBB = bb.getUpBollingerBands();
                    currentLowerBB = bb.getdownBollingerBands();
                    currentMiddleBB = bb.getMidBollingerBands();
                }

                // RSI 계산 (14 기간 - 상태 보존)
                if (priceListFutures.size() >= 15) {
                    currentRSI = rsi14Calculator.calculate(priceListFutures);
                }
                
                // ADX 계산 (14 기간 - 추세 방향 및 강도 감지용)
                if (candlestickListFutures.size() >= 15) {
                    currentADX = adx14Calculator.calculate(candlestickListFutures);
                    currentPlusDI = adx14Calculator.getPlusDI();
                    currentMinusDI = adx14Calculator.getMinusDI();
                }
                
                // 가격 Range 계산 (최근 20개 캔들의 고가-저가 평균 범위)
                if (candlestickListFutures.size() >= 20) {
                    double totalRange = 0.0;
                    int rangeCount = Math.min(20, candlestickListFutures.size());
                    
                    for (int i = candlestickListFutures.size() - rangeCount; i < candlestickListFutures.size(); i++) {
                        Candlestick candle = candlestickListFutures.get(i);
                        double high = Double.parseDouble(candle.getHigh());
                        double low = Double.parseDouble(candle.getLow());
                        double close = Double.parseDouble(candle.getClose());
                        totalRange += (high - low) / close;
                    }
                    
                    priceRangePercent = totalRange / rangeCount;
                }
            }

            // ATR 계산: Wilder's Smoothing을 적용한 표준 ATR (14기간)으로 변경
            double calculatedAtr = calculateATR(candlestickListFutures, 14);
            if (calculatedAtr > 0 && !priceListFutures.isEmpty()) {
                atrValue = calculatedAtr / priceListFutures.getLast(); // 상대 ATR로 변환
            } else {
                atrValue = 0.0; // 계산 불가 시 0으로 초기화
            }

            // 4. 설정 파일 로드
            if(!bfiveInit)
                loadProperties();
            if (!bfiveInit) {
                // 5. 로그 출력 (개선된 거래 시스템)
                asyncPrint("--- Enhanced Trading System Status (Trend Following + Bollinger Band Range) ---");
                asyncPrint(String.format("Volume Limits (Spot/Futures): %.1f / %.1f", volumeBTC_Check, trade_volume_max));
                asyncPrint(String.format("Current Avg Volume (Spot/Futures): %.2f / %.2f", volumeBTC, volume));
                asyncPrint(String.format("Volume Condition: %s", 
                    ((volume < trade_volume_max) || (volumeBTC < volumeBTC_Check)) ? "MET" : "NOT MET"));
                
                // 추세 분석 지표들
                String trendCondition = currentADX > 25.0 ? "TRENDING" : "SIDEWAYS";
                String trendDirection = "";
                if (currentADX > 25.0) {
                    trendDirection = currentPlusDI > currentMinusDI ? " (UPTREND)" : " (DOWNTREND)";
                }
                asyncPrint(String.format("ADX (Trend Strength): %.2f %s%s", currentADX, trendCondition, trendDirection));
                asyncPrint(String.format("+DI: %.2f, -DI: %.2f", currentPlusDI, currentMinusDI));
                asyncPrint(String.format("Price Range: %.3f%% (%s)", 
                    priceRangePercent * 100, 
                    priceRangePercent <= 0.008 ? "LOW VOLATILITY" : "HIGH VOLATILITY"));
                
                if (!Double.isNaN(currentUpperBB) && !Double.isNaN(currentLowerBB)) {
                    asyncPrint(String.format("Bollinger Bands - Upper: %.2f, Middle: %.2f, Lower: %.2f", 
                        currentUpperBB, currentMiddleBB, currentLowerBB));
                    asyncPrint("*** Trading ONLY within Bollinger Bands ***");
                }
                if (currentRSI != 50.0) {
                    String rsiCondition = currentRSI > 70 ? "Overbought" : (currentRSI < 30 ? "Oversold" : "Neutral");
                    asyncPrint(String.format("RSI (14): %.2f (%s)", currentRSI, rsiCondition));
                }
                
                // 거래 전략 상태 표시
                if (currentADX > 25.0) {
                    asyncPrint("Strategy: TREND FOLLOWING - Trading with trend direction");
                } else {
                    boolean sidewaysCondition = (priceRangePercent <= 0.008);
                    asyncPrint(String.format("Strategy: SIDEWAYS TRADING - Condition: %s", sidewaysCondition ? "✓ SUITABLE" : "✗ NOT SUITABLE"));
                }
                
                asyncPrint(String.format("Trading Status: %s | Enabled: %s | Trace Level: %d", 
                    tradeStatus, (isTradingEnabled > 0 ? "YES" : "NO"), TRACE));
                asyncPrint("ATR: " + roundToTwoDecimalPlaces(atrValue * 100) + "%");
                asyncPrint("--------------------------------------");
            }
        } catch (Exception e) {
            asyncPrint("Error occurred during InitMain execution: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static double calculateAverageVolume(List<Candlestick> candlesticks, int count) {
        if (candlesticks == null || candlesticks.isEmpty()) return 0;

        // API는 보통 마지막 캔들로 현재 진행중인 캔들을 반환하므로,
        // 이를 제외하고 완전히 마감된 캔들 기준으로 평균을 계산해야 더 정확합니다.
        int dataSize = candlesticks.size();
        // 데이터가 충분하지 않으면 계산하지 않음 (최소 count+1개의 캔들 필요)
        if (dataSize <= count) {
            return 0;
        }

        double totalVolume = 0;
        // 마지막 캔들(인덱스 dataSize-1)을 제외하고, 그 이전 count개의 캔들을 사용합니다.
        int startIndex = dataSize - 1 - count;
        int endIndex = dataSize - 1;

        for (int i = startIndex; i < endIndex; i++) {
            totalVolume += Double.parseDouble(candlesticks.get(i).getVolume());
        }

        return count > 0 ? roundToTwoDecimalPlaces(totalVolume / count) : 0;
    }
    
    private static void loadProperties() {
        String filePath = "c:/Trade/TradeExpert.properties";
        try (InputStream input = new FileInputStream(filePath)) {
            Properties prop = new Properties();
            prop.load(input);
            volumeBTC_Check = Double.parseDouble(prop.getProperty("volumeBTC_Check", "10")); // Aldar 기본값
            trade_volume_max = Double.parseDouble(prop.getProperty("trade_volume_max", "100.0")); // Aldar 기본값
            isTradingEnabled = Integer.parseInt(prop.getProperty("INIT_WAIT", "0"));
            TRACE = Integer.parseInt(prop.getProperty("TRACE", "0"));
        } catch (IOException | NumberFormatException e) {
            asyncPrint("Configuration file loading error: " + e.getMessage());
        }
    }

    public static void main(String[] args) throws InterruptedException {
        // 바이낸스 클라이언트 생성
        BinancefactoryF = BinanceAbstractFactory.createFuturesFactory("", "");
        clientF = BinancefactoryF.newRestClient();
        factory = BinanceAbstractFactory.createSpotFactory("", "");
        client = factory.newRestClient();

        // UI 실행
        Trade.INSTANCE.TradeBitqDlg_expert();
        Trade.INSTANCE.TradeStart(WINDOWS_TOP);
        Trade.INSTANCE.TradeStart(TIME_RESET);

        asyncPrint("Waiting for initialization (10 seconds)...");
        Thread.sleep(10000);
        asyncPrint("Initialization completed.");

        // 메시지 핸들러 스레드 시작
        MessageHandler messageHandlerInstance = new MessageHandler();
        Thread messageHandlerThread = new Thread(messageHandlerInstance, "MessageHandlerThread");
        messageHandlerThread.start();
        asyncPrint("MessageHandler thread started.");
    }
    
    private static double roundToTwoDecimalPlaces(double value) {
        return Math.round(value * 100.0) / 100.0;
    }
}
