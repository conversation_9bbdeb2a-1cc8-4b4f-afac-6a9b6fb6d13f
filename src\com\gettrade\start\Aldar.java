package com.gettrade.start;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.Socket;
import java.text.DecimalFormat;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.binance.api.client.api.sync.BinanceApiFuturesRestClient;
import com.binance.api.client.api.sync.BinanceApiSpotRestClient;
import com.binance.api.client.domain.market.Candlestick;
import com.binance.api.client.domain.market.CandlestickInterval;
import com.binance.api.client.factory.BinanceAbstractFactory;
import com.binance.api.client.factory.BinanceFuturesApiClientFactory;
import com.binance.api.client.factory.BinanceSpotApiClientFactory;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.stream.JsonReader;
import com.sun.jna.Library;
import com.sun.jna.Native;

public class Aldar {

    public enum TradeAction {
        UP,
        DOWN,
        HOLD
    }

    public interface Trade extends Library {
        Trade INSTANCE = (Trade) Native.loadLibrary("MTrade", Trade.class);

        public void TradeBitqDlg_expert();

        public int GetUpDownSecRcv();

        public int SetUpDownSecRcv();

        public void TradeStart(int nflag, String value);
    }

    public static int BUY_UP = 500301;
    public static int SELL_DOWN = 500302;
    public static int CHECK = 500303;
    //public static int SETMONEY = 500304;
    public static int GETTIME = 500305;
    public static int WINDOWS_TOP = 300008;

    // --- 기술적 분석을 위한 데이터 개수 ---
    public static final int CANDLESTICK_DATA_COUNT = 30; // 30초 거래를 위해 더 줄임 (30분 데이터)

    public static int TIME_RESET = 4;
    public static int REFRESH = 304;
    public static int TRADE_STOP = 103;
    public static volatile int REFRESH_CNT = 0;
    public static volatile double volume = 0; // 전체 퓨처 거래량 (과거 데이터 기반)
    public static volatile double volumeBTC = 0; // 전체 스팟 거래량 (과거 데이터 기반)
    public static volatile double volumeBTC_Check = 15; // 스팟 거래량 임계값 (설정값)
    public static volatile double trade_volume_max = 15; // 퓨처 거래량 임계값 (설정값)

    public static volatile int TradeFlg = 3; // 거래 상태 플래그 (0: 대기, 1: 매수 시도, 2: 매도 시도, 3: 일시 정지)
    public static volatile int INIT_WAIT = 0; // 초기 대기 시간 관련 플래그 (설정값)
    public static volatile int TRACE = 0; // 디버깅 로그 레벨 (설정값)
    public static volatile String MONEY = "2";

    private static volatile double tradeOrderbookDownF20 = Double.NaN;
    private static volatile double tradeOrderbookUpF20 = Double.NaN;
    private static volatile double tradeOrderbookDown20 = Double.NaN;
    private static volatile double tradeOrderbookUp20 = Double.NaN;

    private static volatile boolean bfirstBTC20 = false;
    private static volatile boolean bfirstFBTC20 = false;

    public static volatile double currentADX = 25.0; // ADX 값 (기본값: 횡보)
    public static volatile double atrValue = 0.005; // 기본값 초기화
    private static ADX adx14Calculator = new ADX(14);

    public static volatile double currentSpotPrice = 0.0; // 실시간 현물가
    public static volatile double currentFuturesPrice = 0.0; // 실시간 선물가

    public static BinanceFuturesApiClientFactory BinancefactoryF = null;
    public static BinanceApiFuturesRestClient clientF = null;

    public static BinanceSpotApiClientFactory factory = null;
    public static BinanceApiSpotRestClient client = null;

    public static String symbol = "BTCUSDT";
    public static volatile List<Double> priceListS_global = new CopyOnWriteArrayList<>(); // 현물 가격 리스트 (전역, Thread-safe)
    private static final Object volumeLock = new Object();
    private static final ExecutorService messageProcessingService = Executors.newFixedThreadPool(10);
    private static final ExecutorService PrintService = Executors.newSingleThreadExecutor();

    public static void asyncPrint(String message) {
        PrintService.submit(() -> System.out.println(message));
    }

    // --- MessageHandler (Main Trading Logic) ---
    static class MessageHandler implements Runnable {

        @Override
        public void run() {
            while (true) {
                try (Socket socket = new Socket("127.0.0.1", 22222)) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                    asyncPrint("Data receiving socket connection successful.");
                    resetDataFlags();
                    resetTradeOrderbook();

                    while (true) {
                        String message = in.readLine();
                        if (message != null && !message.isEmpty() && INIT_WAIT == 1 && TradeFlg == 0) {
                            try {
                                processMessage(message);
                            } catch (Exception e) {
                                asyncPrint("Error while processing message: " + e.getMessage());
                                if (TRACE > 0)
                                    e.printStackTrace();
                            }
                        } else {
                            resetDataFlags();
                        }
                    }
                } catch (IOException e) {
                    asyncPrint("Socket connection error or data receiving error: " + e.getMessage());
                    resetTradeOrderbook();
                    resetDataFlags();
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        asyncPrint("Interrupt occurred while waiting for reconnection.");
                    }
                } catch (Exception ex) {
                    asyncPrint("Unexpected error in MessageHandler loop: " + ex.getMessage());
                    ex.printStackTrace();
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }

        private void processMessage(String message) {
            if (message == null || message.isEmpty()) {
                asyncPrint("Empty message received, skipping processing.");
                return;
            }

            if (volume > trade_volume_max || volumeBTC > volumeBTC_Check) {
                if (TRACE > 0)
                    asyncPrint("Total volume exceeded (F:" + volume + ", S:" + volumeBTC
                            + "), trading temporarily paused. TradeFlg=3");
                TradeFlg = 3;
            }

            try (JsonReader reader = new JsonReader(new StringReader(message))) {
                reader.setLenient(true);

                while (reader.hasNext()) {
                    JsonObject jsonObject = null;
                    try {
                        jsonObject = JsonParser.parseReader(reader).getAsJsonObject();
                    } catch (Exception e) {
                        if (TRACE > 0) {
                            asyncPrint("JSON parsing error in stream: " + e.getMessage());
                        }
                        continue;
                    }

                    if (jsonObject.has("Mtype") && jsonObject.has("message")) {
                        String mtype = jsonObject.get("Mtype").getAsString();
                        String msg = jsonObject.get("message").getAsString();

                        try {
                            if ("BTC20".equals(mtype)) {
                                String[] orderbook20MSg = msg.split("-");
                                if (validateOrderbookData(orderbook20MSg)) {
                                    tradeOrderbookDown20 = Double.parseDouble(orderbook20MSg[0]);
                                    tradeOrderbookUp20 = Double.parseDouble(orderbook20MSg[1]);
                                    currentSpotPrice = Double.parseDouble(orderbook20MSg[2]);
                                    bfirstBTC20 = true;
                                } else {
                                    asyncPrint("Invalid or incomplete spot data: " + msg);
                                }
                            } else if ("FBTC20".equals(mtype)) {
                                String[] orderbook20MSg = msg.split("-");
                                if (validateOrderbookData(orderbook20MSg)) {
                                    tradeOrderbookDownF20 = Double.parseDouble(orderbook20MSg[0]);
                                    tradeOrderbookUpF20 = Double.parseDouble(orderbook20MSg[1]);
                                    currentFuturesPrice = Double.parseDouble(orderbook20MSg[2]);
                                    bfirstFBTC20 = true;
                                } else {
                                    asyncPrint("Invalid or incomplete futures data: " + msg);
                                }
                            }
                        } catch (NumberFormatException e) {
                            asyncPrint(
                                    "Number format error while parsing data: " + msg + " / Error: " + e.getMessage());
                            return;
                        } catch (Exception e) {
                            asyncPrint("Exception while processing data: " + msg + " / Error: " + e.getMessage());
                            return;
                        }

                        if (bfirstBTC20 && bfirstFBTC20) {
                            if (Double.isNaN(tradeOrderbookUp20) || Double.isNaN(tradeOrderbookDown20)
                                    || Double.isNaN(tradeOrderbookUpF20)
                                    || Double.isNaN(tradeOrderbookDownF20)) {
                                return;
                            }

                            if (TradeFlg == 0 && INIT_WAIT > 0) {
                                if (currentADX < 20 && atrValue < 0.0005 && volume < 100 && volumeBTC < 10) {
                                    if (tradeOrderbookUp20 < 1 && tradeOrderbookUpF20 < 1)
                                        executeTrade(true);
                                    else if (tradeOrderbookDown20 < 1 && tradeOrderbookDownF20 < 1)
                                        executeTrade(false);
                                }
                            }
                        }
                    }
                }
            } catch (IOException e) {
                asyncPrint("IOException during JSON stream processing: " + e.getMessage());
            }
        }

        private boolean validateOrderbookData(String[] data) {
            if (data.length < 3)
                return false;
            try {
                for (int i = 0; i < 3; i++) {
                    double value = Double.parseDouble(data[i]);
                    if (value < 0 || Double.isNaN(value)) { // Use Double.NaN for invalid values
                        return false;
                    }
                }
                return true;
            } catch (NumberFormatException e) {
                return false;
            }
        }

        private void executeTrade(boolean isBuySignal) {
            if (TradeFlg == 0 && INIT_WAIT > 0) {
                TradeFlg = 3;
                if (isBuySignal) {
                    Trade.INSTANCE.TradeStart(BUY_UP, "");
                    asyncPrint("### BUY executed at " + new java.util.Date() + " ###");
                } else {
                    Trade.INSTANCE.TradeStart(SELL_DOWN, "");
                    asyncPrint("### SELL executed at " + new java.util.Date() + " ###");
                }
            } else {
                if (TRACE > 0)
                    asyncPrint("Trade not executed: TradeFlg=" + TradeFlg + ", INIT_WAIT=" + INIT_WAIT);
            }
        }
    }

    private static void resetTradeOrderbook() {
        tradeOrderbookDownF20 = Double.NaN;
        tradeOrderbookUpF20 = Double.NaN;
        tradeOrderbookDown20 = Double.NaN;
        tradeOrderbookUp20 = Double.NaN;
    }

    private static void resetDataFlags() {
        bfirstBTC20 = false;
        bfirstFBTC20 = false;
    }

    public static void InitMain() {
        try {
            REFRESH_CNT++;
            double localVolume = 0;
            double localVolumeBTC = 0;
            List<Candlestick> candlestickListF = clientF.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE,
                    CANDLESTICK_DATA_COUNT, null, null);
            List<Candlestick> candlestickListS = client.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE,
                    CANDLESTICK_DATA_COUNT, null, null);

            List<Double> priceListF = new ArrayList<>();
            int countF = 0;
            if (candlestickListF != null && !candlestickListF.isEmpty()) {
                for (int i = 0; i < candlestickListF.size(); i++) {
                    Candlestick candlestickF = candlestickListF.get(i);
                    // Use 10 candles for a more stable average volume
                    if (i >= candlestickListF.size() - 11 && i < candlestickListF.size() - 1) {
                        localVolume += Double.parseDouble(candlestickF.getVolume());
                        countF++;
                    }
                    priceListF.add(Double.parseDouble(candlestickF.getClose()));
                }
            }
            if (countF > 0)
                localVolume = localVolume / countF;
            localVolume = roundToTwoDecimalPlaces(localVolume);

            List<Double> newPriceListS = new ArrayList<>();
            int countS = 0;
            if (candlestickListS != null && !candlestickListS.isEmpty()) {
                for (int i = 0; i < candlestickListS.size(); i++) {
                    Candlestick candlestickS = candlestickListS.get(i);
                    // Use 10 candles for a more stable average volume
                    if (i >= candlestickListS.size() - 11 && i < candlestickListS.size() - 1) {
                        localVolumeBTC += Double.parseDouble(candlestickS.getVolume());
                        countS++;
                    }
                    newPriceListS.add(Double.parseDouble(candlestickS.getClose()));
                }
            }
            priceListS_global = new CopyOnWriteArrayList<>(newPriceListS);
            if (countS > 0)
                localVolumeBTC = localVolumeBTC / countS;
            localVolumeBTC = roundToTwoDecimalPlaces(localVolumeBTC);

            synchronized (volumeLock) {
                volume = localVolume;
                volumeBTC = localVolumeBTC;
            }

            // ATR 계산: Wilder's Smoothing을 적용한 표준 ATR (14기간)으로 변경
            double calculatedAtr = calculateATR(candlestickListF, 14);
            if (calculatedAtr > 0 && !priceListF.isEmpty()) {
                atrValue = calculatedAtr / priceListF.getLast(); // 상대 ATR로 변환
            } else {
                atrValue = 0.0; // 계산 불가 시 0으로 초기화
            }

            // ADX 계산 (14 기간 - 추세 방향 및 강도 감지용)
            if (candlestickListF.size() >= 15) {
                currentADX = adx14Calculator.calculate(candlestickListF);
            }

            String filePath = "c:/Trade/Aldar.properties";
            try (InputStream input = new FileInputStream(filePath)) {
                Properties prop = new Properties();
                prop.load(input);
                volumeBTC_Check = Double.parseDouble(prop.getProperty("volumeBTC_Check", "15"));
                trade_volume_max = Double.parseDouble(prop.getProperty("trade_volume_max", "15"));
                INIT_WAIT = Integer.parseInt(prop.getProperty("INIT_WAIT", "0"));
                TRACE = Integer.parseInt(prop.getProperty("TRACE", "0"));
                MONEY = prop.getProperty("MONEY", "2");

            } catch (IOException | NumberFormatException e) {
                asyncPrint("Error loading or parsing configuration file: " + e.getMessage());
            }

            if (TRACE > 0) {
                asyncPrint("--- Settings for 30-Second Binary Options ---");
                asyncPrint(String.format("Volume Limits (Spot/Future): %.1f/%.1f", volumeBTC_Check, trade_volume_max));
                asyncPrint(String.format("Current Avg Volume (Spot/Future): %.2f/%.2f", volumeBTC, volume));
                asyncPrint("MONEY: " + MONEY);
                asyncPrint("--------------------------");
            }
        } catch (Exception e) {
            asyncPrint("Error during InitMain execution: " + e.getMessage());
            if (TRACE > 1)
                e.printStackTrace();
            synchronized (volumeLock) {
                volume = 0;
                volumeBTC = 0;
            }
        }
    }

    // ADX (Average Directional Index) 클래스 - 추세 방향 및 강도 감지용
    public static class ADX {
        private final int period;
        private double currentPlusDI = 0.0;
        private double currentMinusDI = 0.0;
        private double currentADXValue = 25.0;

        public ADX(int period) {
            this.period = period;
        }

        /**
         * Wilder's Smoothing (EMA with alpha = 1/period)을 적용하는 헬퍼 메소드
         */
        private double[] getSmoothedValues(List<Double> values, int period) {
            double[] smoothed = new double[values.size()];
            if (values.isEmpty() || values.size() < period)
                return smoothed;

            // 첫 값은 SMA(단순 이동 평균)로 초기화
            double sum = 0;
            for (int i = 0; i < period; i++) {
                sum += values.get(i);
            }
            smoothed[period - 1] = sum / period;

            // 이후 값들은 Wilder's Smoothing 적용 (표준 EMA 공식)
            for (int i = period; i < values.size(); i++) {
                smoothed[i] = (smoothed[i - 1] * (period - 1) + values.get(i)) / period;
            }
            return smoothed;
        }

        public double calculate(List<Candlestick> candlesticks) {
            // 안정적인 ADX 계산을 위해 최소 2 * period + 1 개의 캔들 필요
            if (candlesticks.size() < (period * 2) + 1) {
                currentADXValue = 25.0; // 데이터 부족 시 중립 값 반환
                currentPlusDI = 50.0;
                currentMinusDI = 50.0;
                return currentADXValue;
            }

            List<Double> highList = new ArrayList<>();
            List<Double> lowList = new ArrayList<>();
            List<Double> closeList = new ArrayList<>();
            for (Candlestick c : candlesticks) {
                highList.add(Double.parseDouble(c.getHigh()));
                lowList.add(Double.parseDouble(c.getLow()));
                closeList.add(Double.parseDouble(c.getClose()));
            }

            List<Double> trList = new ArrayList<>();
            List<Double> plusDMList = new ArrayList<>();
            List<Double> minusDMList = new ArrayList<>();

            for (int i = 1; i < candlesticks.size(); i++) {
                double high = highList.get(i);
                double low = lowList.get(i);
                double prevHigh = highList.get(i - 1);
                double prevLow = lowList.get(i - 1);
                double prevClose = closeList.get(i - 1);

                // True Range (TR)
                double tr = Math.max(high - low, Math.max(Math.abs(high - prevClose), Math.abs(low - prevClose)));
                trList.add(tr);

                // Directional Movement (+DM, -DM)
                double upMove = high - prevHigh;
                double downMove = prevLow - low;
                plusDMList.add((upMove > downMove && upMove > 0) ? upMove : 0.0);
                minusDMList.add((downMove > upMove && downMove > 0) ? downMove : 0.0);
            }

            // TR, +DM, -DM 스무딩 (ATR, Smoothed +DM, Smoothed -DM)
            double[] smoothedTR = getSmoothedValues(trList, period);
            double[] smoothedPlusDM = getSmoothedValues(plusDMList, period);
            double[] smoothedMinusDM = getSmoothedValues(minusDMList, period);

            List<Double> dxList = new ArrayList<>();
            for (int i = period - 1; i < smoothedTR.length; i++) {
                if (smoothedTR[i] > 0) {
                    double plusDI = 100 * (smoothedPlusDM[i] / smoothedTR[i]);
                    double minusDI = 100 * (smoothedMinusDM[i] / smoothedTR[i]);
                    double sumDI = plusDI + minusDI;
                    if (sumDI != 0) {
                        dxList.add(100 * Math.abs(plusDI - minusDI) / sumDI);
                        // 마지막 값들을 저장 (추세 방향 판단용)
                        if (i == smoothedTR.length - 1) {
                            currentPlusDI = plusDI;
                            currentMinusDI = minusDI;
                        }
                    } else {
                        dxList.add(0.0);
                    }
                } else {
                    dxList.add(0.0);
                }
            }

            if (dxList.size() < period) {
                currentADXValue = 25.0; // 데이터 부족
                currentPlusDI = 50.0;
                currentMinusDI = 50.0;
                return currentADXValue;
            }

            // DX를 스무딩하여 ADX 계산
            double[] adxValues = getSmoothedValues(dxList, period);

            // 마지막 계산된 ADX 값을 반환 (인덱스 주의)
            currentADXValue = adxValues[adxValues.length - 1];
            return currentADXValue;
        }

        public double getPlusDI() {
            return currentPlusDI;
        }

        public double getMinusDI() {
            return currentMinusDI;
        }

        public boolean isUpTrend() {
            return currentPlusDI > currentMinusDI;
        }

        public boolean isDownTrend() {
            return currentMinusDI > currentPlusDI;
        }
    }

    // Wilder's Smoothing을 사용한 ATR 계산 헬퍼 메소드
    private static double calculateATR(List<Candlestick> candlesticks, int period) {
        if (candlesticks == null || candlesticks.size() < period + 1) {
            return 0.0; // ATR 계산에 필요한 최소 데이터가 부족한 경우
        }

        List<Double> trueRanges = new ArrayList<>();
        for (int i = 1; i < candlesticks.size(); i++) {
            Candlestick current = candlesticks.get(i);
            Candlestick previous = candlesticks.get(i - 1);

            double high = Double.parseDouble(current.getHigh());
            double low = Double.parseDouble(current.getLow());
            double prevClose = Double.parseDouble(previous.getClose());

            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);
            double tr = Math.max(tr1, Math.max(tr2, tr3));
            trueRanges.add(tr);
        }

        if (trueRanges.size() < period) {
            return 0.0;
        }

        // 초기 ATR은 처음 'period'개의 TR의 SMA로 계산
        double atr = 0.0;
        for (int i = 0; i < period; i++) {
            atr += trueRanges.get(i);
        }
        atr /= period;

        // 이후 ATR은 Wilder's Smoothing 적용
        for (int i = period; i < trueRanges.size(); i++) {
            atr = (atr * (period - 1) + trueRanges.get(i)) / period;
        }

        return atr;
    }

    public static void main(String[] args) throws InterruptedException, IOException {
        BinancefactoryF = BinanceAbstractFactory.createFuturesFactory("", "");
        clientF = BinancefactoryF.newRestClient();
        factory = BinanceAbstractFactory.createSpotFactory("", "");
        client = factory.newRestClient();

        Trade.INSTANCE.TradeBitqDlg_expert();
        Trade.INSTANCE.TradeStart(WINDOWS_TOP, "");
        Trade.INSTANCE.TradeStart(TIME_RESET, "");

        asyncPrint("Waiting for initialization (10 seconds)...");
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            asyncPrint("Initialization sleep interrupted.");
            if (TRACE > 1)
                e.printStackTrace();
        }
        asyncPrint("Initialization wait complete.");

        Trade.INSTANCE.SetUpDownSecRcv();
        new Thread(() -> {
            while (true) {
                try {
                    InitMain();
                    Trade.INSTANCE.TradeStart(CHECK, "");
                    Thread.sleep(5000);
                    int upDownSecRcv = Trade.INSTANCE.GetUpDownSecRcv();
                    if (upDownSecRcv == 100) {
                        if (REFRESH_CNT > 10) {
                            Trade.INSTANCE.TradeStart(REFRESH, "");
                            REFRESH_CNT = 0;
                            asyncPrint("MTrade REFRESH called.");
                            Thread.sleep(5000);
                        }
                        TradeFlg = 3;
                        INIT_WAIT = 0;
                        if (TRACE > 0)
                            asyncPrint("State 100: Waiting for next signal. TradeFlg=3, INIT_WAIT=0.");
                    } else if (upDownSecRcv == 200) {
                        long startTime = System.currentTimeMillis();
                        long timeout = 30000;
                        if (TRACE > 0)
                            asyncPrint("State 200: Order placement window. Timeout: " + timeout / 1000 + "s");

                        boolean orderPlacedSignalReceived = false;
                        boolean isInitMainCalled = false;
                        while (System.currentTimeMillis() - startTime < timeout) {
                            Trade.INSTANCE.TradeStart(GETTIME, "");
                            Thread.sleep(50);
                            int timerSignal = Trade.INSTANCE.GetUpDownSecRcv();
                            if (timerSignal == 2 && !isInitMainCalled) {
                                InitMain();
                                resetDataFlags();
                                resetTradeOrderbook();
                                isInitMainCalled = true;
                            }
                            if (timerSignal == 1) {
                                INIT_WAIT = 1;
                                TradeFlg = 0;
                                Trade.INSTANCE.SetUpDownSecRcv();
                                asyncPrint(
                                        "Signal 1 received from MTrade. INIT_WAIT=1, TradeFlg=0. Ready for MessageHandler to trade.");
                                orderPlacedSignalReceived = true;
                                Thread.sleep(2000);
                                TradeFlg = 3;
                                INIT_WAIT = 0;
                                break;
                            }
                        }
                        if (!orderPlacedSignalReceived && TRACE > 0) {
                            asyncPrint("State 200 timeout: No trade signal (1) received from MTrade within "
                                    + timeout / 1000 + "s.");
                            TradeFlg = 3;
                            INIT_WAIT = 0;
                        }
                    } else {
                        if (TRACE > 0)
                            asyncPrint("Unhandled GetUpDownSecRcv value: " + upDownSecRcv);
                        TradeFlg = 3;
                        INIT_WAIT = 0;
                    }
                } catch (InterruptedException e) {
                    asyncPrint("MTrade control thread interrupted.");
                    if (TRACE > 1)
                        e.printStackTrace();
                    Thread.currentThread().interrupt();
                    break;
                } catch (UnsatisfiedLinkError ule) {
                    asyncPrint("Native library MTrade error: " + ule.getMessage());
                    if (TRACE > 1)
                        ule.printStackTrace();
                    break;
                } catch (Exception e) {
                    asyncPrint("Error in MTrade control thread: " + e.getMessage());
                    if (TRACE > 1)
                        e.printStackTrace();
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            asyncPrint("MTrade control thread terminated.");
        }).start();

        MessageHandler messageHandlerInstance = new MessageHandler();
        Thread messageHandlerThread = new Thread(messageHandlerInstance);
        messageHandlerThread.setName("MessageHandlerThread");
        messageHandlerThread.start();
        asyncPrint("MessageHandler thread started.");

        asyncPrint("Main thread waiting started.");
        while (true) {
            try {
                Thread.sleep(60000);
                if (TRACE > 2)
                    asyncPrint("Main thread still alive. Current TradeFlg: " + TradeFlg + ", INIT_WAIT: " + INIT_WAIT);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                asyncPrint("Main thread interrupted. Shutting down...");
                if (messageHandlerThread != null)
                    messageHandlerThread.interrupt();
                messageProcessingService.shutdownNow();
                PrintService.shutdownNow();
                break;
            }
        }
        asyncPrint("Main thread terminated.");
    }

    private static double roundToTwoDecimalPlaces(double value) {
        DecimalFormat df_local = new DecimalFormat("#.##");
        try {
            return Double.parseDouble(df_local.format(value));
        } catch (NumberFormatException e) {
            return value;
        }
    }
}
